import React from 'react';

interface SkeletonLoaderProps {
  variant?: 'text' | 'rectangular' | 'circular' | 'card' | 'list';
  width?: string | number;
  height?: string | number;
  count?: number;
  className?: string;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  variant = 'text',
  width,
  height,
  count = 1,
  className = ''
}) => {
  const baseClasses = 'animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%]';
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'h-4 rounded';
      case 'rectangular':
        return 'rounded-lg';
      case 'circular':
        return 'rounded-full';
      case 'card':
        return 'h-48 rounded-2xl';
      case 'list':
        return 'h-16 rounded-xl';
      default:
        return 'h-4 rounded';
    }
  };

  const getDefaultWidth = () => {
    switch (variant) {
      case 'circular':
        return height || '3rem';
      case 'card':
        return '100%';
      case 'list':
        return '100%';
      default:
        return '100%';
    }
  };

  const getDefaultHeight = () => {
    switch (variant) {
      case 'circular':
        return width || '3rem';
      case 'text':
        return '1rem';
      case 'rectangular':
        return '2rem';
      case 'card':
        return '12rem';
      case 'list':
        return '4rem';
      default:
        return '1rem';
    }
  };

  const skeletonStyle = {
    width: width || getDefaultWidth(),
    height: height || getDefaultHeight(),
  };

  if (variant === 'card') {
    return (
      <div className={`${baseClasses} ${getVariantClasses()} ${className} p-6`} style={skeletonStyle}>
        <div className="space-y-4">
          <div className="h-6 bg-gray-300 rounded w-3/4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
          </div>
          <div className="flex space-x-4 pt-4">
            <div className="h-8 bg-gray-300 rounded w-20"></div>
            <div className="h-8 bg-gray-300 rounded w-24"></div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'list') {
    return (
      <div className="space-y-3">
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className={`${baseClasses} ${getVariantClasses()} ${className} p-4`}>
            <div className="flex items-center space-x-4">
              <div className="h-10 w-10 bg-gray-300 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-3 bg-gray-300 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className={`${baseClasses} ${getVariantClasses()} ${className}`}
          style={skeletonStyle}
        />
      ))}
    </div>
  );
};

// Specialized skeleton components
export const DocumentCardSkeleton: React.FC = () => (
  <div className="bg-white rounded-2xl p-6 shadow-lg border-2 border-gray-100">
    <div className="animate-pulse">
      <div className="flex items-start space-x-4">
        <div className="w-12 h-12 bg-gray-300 rounded-2xl"></div>
        <div className="flex-1 space-y-3">
          <div className="flex items-center space-x-3">
            <div className="h-6 bg-gray-300 rounded w-2/3"></div>
            <div className="h-6 bg-gray-300 rounded-full w-16"></div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-4/5"></div>
          </div>
          <div className="flex items-center space-x-4 pt-2">
            <div className="h-3 bg-gray-300 rounded w-20"></div>
            <div className="h-3 bg-gray-300 rounded w-24"></div>
          </div>
        </div>
      </div>
      <div className="flex justify-end space-x-2 mt-4">
        <div className="h-8 bg-gray-300 rounded w-20"></div>
        <div className="h-8 bg-gray-300 rounded w-24"></div>
      </div>
    </div>
  </div>
);

export const FilterPanelSkeleton: React.FC = () => (
  <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-100/50 p-8 mb-8">
    <div className="animate-pulse">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-300 rounded-2xl"></div>
          <div className="space-y-2">
            <div className="h-6 bg-gray-300 rounded w-48"></div>
            <div className="h-4 bg-gray-300 rounded w-64"></div>
          </div>
        </div>
        <div className="flex space-x-2">
          <div className="h-8 bg-gray-300 rounded-xl w-32"></div>
          <div className="h-8 bg-gray-300 rounded-xl w-20"></div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="space-y-3">
            <div className="h-4 bg-gray-300 rounded w-24"></div>
            <div className="h-12 bg-gray-300 rounded-2xl"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default SkeletonLoader;
