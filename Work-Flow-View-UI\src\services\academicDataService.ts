import { apiClient, ApiResponse } from './api';

// Types matching the API DTOs
export interface AcademicYear {
  id: number;
  name: string;
  code: string;
  startYear: number;
  endYear: number;
  levelId: number;
  status: string;
  description?: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Department {
  id: number;
  name: string;
  code: string;
  description?: string;
  headOfDepartment?: string;
  email?: string;
  phone?: string;
  establishedYear?: number;
  programsOffered?: string;
  accreditation?: string;
  status: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  levelId: number;
}

export interface Course {
  id: number;
  name: string;
  code: string;
  description?: string;
  credits: number;
  courseType: string;
  durationWeeks: number;
  maxCapacity: number;
  status: string;
  prerequisites?: string;
  learningObjectives?: string;
  learningOutcomes?: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  departmentName?: string;
  departmentCode?: string;
}

export interface Semester {
  id: number;
  name: string;
  code: string;
  departmentId: number;
  courseId?: number[];
  startDate: string;
  endDate: string;
  durationWeeks: number;
  totalStudents: number;
  status: string;
  description?: string;
  examScheduled: boolean;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  academicYearId: number;
  levelId: number;
}

export interface AcademicDataFilter {
  academicYearId?: number;
  semesterId?: number;
  courseId?: number;
  departmentId?: number;
  degreeId?: number;
  isActive?: boolean;
}

export interface AcademicDataResponse {
  academicYears: AcademicYear[];
  departments: Department[];
  courses: Course[];
  semesters: Semester[];
}

// Academic Data Service
export class AcademicDataService {
  // Academic Years
  async getAllAcademicYears(): Promise<AcademicYear[]> {
    const response = await apiClient.get<AcademicYear[]>('/academic-data/academic-years');
    return response.data;
  }

  async getActiveAcademicYears(): Promise<AcademicYear[]> {
    const response = await apiClient.get<AcademicYear[]>('/academic-data/academic-years/active');
    return response.data;
  }

  async getAcademicYearById(id: number): Promise<AcademicYear> {
    const response = await apiClient.get<AcademicYear>(`/academic-data/academic-years/${id}`);
    return response.data;
  }

  // Departments
  async getAllDepartments(): Promise<Department[]> {
    const response = await apiClient.get<Department[]>('/academic-data/departments');
    return response.data;
  }

  async getActiveDepartments(): Promise<Department[]> {
    const response = await apiClient.get<Department[]>('/academic-data/departments/active');
    return response.data;
  }

  async getDepartmentById(id: number): Promise<Department> {
    const response = await apiClient.get<Department>(`/academic-data/departments/${id}`);
    return response.data;
  }

  // Courses
  async getAllCourses(): Promise<Course[]> {
    const response = await apiClient.get<Course[]>('/academic-data/courses');
    return response.data;
  }

  async getActiveCourses(): Promise<Course[]> {
    const response = await apiClient.get<Course[]>('/academic-data/courses/active');
    return response.data;
  }

  async getCoursesByDepartment(departmentId: number): Promise<Course[]> {
    const response = await apiClient.get<Course[]>(`/academic-data/courses/department/${departmentId}`);
    return response.data;
  }

  async getCourseById(id: number): Promise<Course> {
    const response = await apiClient.get<Course>(`/academic-data/courses/${id}`);
    return response.data;
  }

  // Semesters
  async getAllSemesters(): Promise<Semester[]> {
    const response = await apiClient.get<Semester[]>('/academic-data/semesters');
    return response.data;
  }

  async getActiveSemesters(): Promise<Semester[]> {
    const response = await apiClient.get<Semester[]>('/academic-data/semesters/active');
    return response.data;
  }

  async getSemestersByDepartment(departmentId: number): Promise<Semester[]> {
    const response = await apiClient.get<Semester[]>(`/academic-data/semesters/department/${departmentId}`);
    return response.data;
  }

  async getSemestersByCourse(courseId: number): Promise<Semester[]> {
    const response = await apiClient.get<Semester[]>(`/academic-data/semesters/course/${courseId}`);
    return response.data;
  }

  async getSemestersByAcademicYear(academicYear: string): Promise<Semester[]> {
    const response = await apiClient.get<Semester[]>(`/academic-data/semesters/academic-year/${academicYear}`);
    return response.data;
  }

  async getSemesterById(id: number): Promise<Semester> {
    const response = await apiClient.get<Semester>(`/academic-data/semesters/${id}`);
    return response.data;
  }

  // Filtered Data
  async getFilteredAcademicData(filter: AcademicDataFilter): Promise<AcademicDataResponse> {
    const response = await apiClient.post<AcademicDataResponse>('/academic-data/filter', filter);
    return response.data;
  }

  async getFilteredAcademicDataByQuery(filter: AcademicDataFilter): Promise<AcademicDataResponse> {
    const params = new URLSearchParams();
    
    if (filter.academicYearId) params.append('academicYearId', filter.academicYearId.toString());
    if (filter.semesterId) params.append('semesterId', filter.semesterId.toString());
    if (filter.courseId) params.append('courseId', filter.courseId.toString());
    if (filter.departmentId) params.append('departmentId', filter.departmentId.toString());
    if (filter.degreeId) params.append('degreeId', filter.degreeId.toString());
    if (filter.isActive !== undefined) params.append('isActive', filter.isActive.toString());

    const queryString = params.toString();
    const endpoint = queryString ? `/academic-data/filter?${queryString}` : '/academic-data/filter';
    
    const response = await apiClient.get<AcademicDataResponse>(endpoint);
    return response.data;
  }
}

export const academicDataService = new AcademicDataService();
