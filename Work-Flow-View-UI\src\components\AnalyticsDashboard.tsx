import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Eye, Download, Search, Users, Clock, Star } from 'lucide-react';
import { analyticsService, DocumentAnalytics, UserAnalytics } from '../services/analyticsService';

const AnalyticsDashboard: React.FC = () => {
  const [documentAnalytics, setDocumentAnalytics] = useState<DocumentAnalytics[]>([]);
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(null);
  const [searchAnalytics, setSearchAnalytics] = useState<any>(null);
  const [popularDocuments, setPopularDocuments] = useState<DocumentAnalytics[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'documents' | 'search' | 'user'>('overview');

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = () => {
    setDocumentAnalytics(analyticsService.getDocumentAnalytics());
    setUserAnalytics(analyticsService.getUserAnalytics());
    setSearchAnalytics(analyticsService.getSearchAnalytics());
    setPopularDocuments(analyticsService.getPopularDocuments(5));
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    trend?: number;
    color?: string;
  }> = ({ title, value, icon, trend, color = 'blue' }) => (
    <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {trend !== undefined && (
            <div className={`flex items-center mt-2 text-sm ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              <TrendingUp className="h-4 w-4 mr-1" />
              {trend >= 0 ? '+' : ''}{trend}%
            </div>
          )}
        </div>
        <div className={`p-3 rounded-2xl bg-${color}-100`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
        <p className="text-gray-600">Track document usage, user behavior, and system performance</p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-2xl">
        {[
          { id: 'overview', label: 'Overview', icon: BarChart3 },
          { id: 'documents', label: 'Documents', icon: Eye },
          { id: 'search', label: 'Search', icon: Search },
          { id: 'user', label: 'User Activity', icon: Users }
        ].map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-colors ${
                activeTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span className="font-medium">{tab.label}</span>
            </button>
          );
        })}
      </div>

      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Documents"
              value={documentAnalytics.length}
              icon={<Eye className="h-6 w-6 text-blue-600" />}
              color="blue"
            />
            <StatCard
              title="Total Views"
              value={formatNumber(documentAnalytics.reduce((sum, doc) => sum + doc.views, 0))}
              icon={<Eye className="h-6 w-6 text-green-600" />}
              color="green"
            />
            <StatCard
              title="Total Downloads"
              value={formatNumber(documentAnalytics.reduce((sum, doc) => sum + doc.downloads, 0))}
              icon={<Download className="h-6 w-6 text-purple-600" />}
              color="purple"
            />
            <StatCard
              title="Search Queries"
              value={searchAnalytics?.totalSearches || 0}
              icon={<Search className="h-6 w-6 text-orange-600" />}
              color="orange"
            />
          </div>

          {/* Popular Documents */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Star className="h-5 w-5 text-yellow-500 mr-2" />
              Most Popular Documents
            </h3>
            <div className="space-y-3">
              {popularDocuments.map((doc, index) => (
                <div key={doc.documentId} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold text-sm">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{doc.title}</p>
                      <p className="text-sm text-gray-600">{doc.type}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{doc.views} views</p>
                    <p className="text-xs text-gray-600">{doc.downloads} downloads</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'documents' && (
        <div className="space-y-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Performance</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Document</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Views</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Downloads</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Last Accessed</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Popularity</th>
                  </tr>
                </thead>
                <tbody>
                  {documentAnalytics.slice(0, 20).map((doc) => (
                    <tr key={doc.documentId} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900 truncate max-w-xs">{doc.title}</div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          {doc.type}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-700">{doc.views}</td>
                      <td className="py-3 px-4 text-gray-700">{doc.downloads}</td>
                      <td className="py-3 px-4 text-gray-700">
                        {new Date(doc.lastAccessed).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${Math.min(100, (doc.popularityScore / 10) * 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600">{doc.popularityScore}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'search' && searchAnalytics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard
              title="Total Searches"
              value={searchAnalytics.totalSearches}
              icon={<Search className="h-6 w-6 text-blue-600" />}
            />
            <StatCard
              title="Unique Queries"
              value={searchAnalytics.uniqueQueries}
              icon={<TrendingUp className="h-6 w-6 text-green-600" />}
            />
            <StatCard
              title="Avg Results"
              value={searchAnalytics.averageResultsPerSearch.toFixed(1)}
              icon={<BarChart3 className="h-6 w-6 text-purple-600" />}
            />
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Search Queries</h3>
            <div className="space-y-3">
              {searchAnalytics.popularQueries.map((query: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                  <span className="font-medium text-gray-900">"{query.query}"</span>
                  <span className="text-sm text-gray-600">{query.count} searches</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'user' && userAnalytics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Sessions"
              value={userAnalytics.totalSessions}
              icon={<Users className="h-6 w-6 text-blue-600" />}
            />
            <StatCard
              title="Documents Viewed"
              value={userAnalytics.totalDocumentsViewed}
              icon={<Eye className="h-6 w-6 text-green-600" />}
            />
            <StatCard
              title="Total Downloads"
              value={userAnalytics.totalDownloads}
              icon={<Download className="h-6 w-6 text-purple-600" />}
            />
            <StatCard
              title="Avg Session"
              value={formatDuration(userAnalytics.averageSessionDuration)}
              icon={<Clock className="h-6 w-6 text-orange-600" />}
            />
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Favorite Document Types</h3>
            <div className="space-y-3">
              {userAnalytics.favoriteDocumentTypes.map((type, index) => (
                <div key={type} className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold text-sm">
                    {index + 1}
                  </div>
                  <span className="font-medium text-gray-900">{type}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsDashboard;
