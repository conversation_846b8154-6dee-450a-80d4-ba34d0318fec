using Microsoft.AspNetCore.Mvc;
using WorkFlowViewMgmt.WebAPI.Services;

namespace WorkFlowViewMgmt.WebAPI.Controllers
{
    [Route("api/files")]
    public class FileController : BaseApiController
    {
        private readonly IFileService _fileService;
        private readonly ILogger<FileController> _logger;

        public FileController(IFileService fileService, ILogger<FileController> logger)
        {
            _fileService = fileService;
            _logger = logger;
        }

        /// <summary>
        /// Get a file by URL path
        /// </summary>
        /// <param name="path">The URL path to the file (e.g., /uploads/syllabi/folder/filename.pdf)</param>
        /// <returns>The file content</returns>
        [HttpGet]
        public async Task<IActionResult> GetFile([FromQuery] string path)
        {
            try
            {
                if (string.IsNullOrEmpty(path))
                {
                    return BadRequest("Path parameter is required");
                }

                var fileResult = await _fileService.GetFileAsync(path);

                if (!fileResult.Exists)
                {
                    return NotFound($"File not found at path '{path}'");
                }

                // Return file for inline viewing (not as attachment)
                return File(fileResult.Content, fileResult.ContentType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving file from path {Path}", path);
                return StatusCode(500, "An error occurred while retrieving the file");
            }
        }
    }
}
