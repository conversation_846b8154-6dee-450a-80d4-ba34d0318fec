using MediatR;
using WorkFlowViewMgmt.Application.Common.Models;
using WorkFlowViewMgmt.Domain.Entities.Degree;
using WorkFlowViewMgmt.Domain.Entities.Filter;
using WorkFlowViewMgmt.Domain.Interface.IUnitOfWork;

namespace WorkFlowViewMgmt.Application.Features.Filter
{
    public class GetActiveDegreesCommandHandler : IRequestHandler<GetActiveDegreesCommand, ApiResponse<List<Degree>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetActiveDegreesCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<Degree>>> Handle(GetActiveDegreesCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var degrees = await _unitOfWork.FilterRepository.GetActiveDegreesAsync();
                return ApiResponse<List<Degree>>.SuccessResponse(degrees.ToList(), "Active degrees retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<Degree>>.ErrorResponse($"Error during fetching active degrees: {ex.Message}");
            }
        }
    }

    public class GetActiveDepartmentsCommandHandler : IRequestHandler<GetActiveDepartmentsCommand, ApiResponse<List<Department>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetActiveDepartmentsCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<Department>>> Handle(GetActiveDepartmentsCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var departments = await _unitOfWork.FilterRepository.GetActiveDepartmentsAsync();
                return ApiResponse<List<Department>>.SuccessResponse(departments.ToList(), "Active departments retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<Department>>.ErrorResponse($"Error during fetching active departments: {ex.Message}");
            }
        }
    }

    public class GetDepartmentsByDegreeCommandHandler : IRequestHandler<GetDepartmentsByDegreeCommand, ApiResponse<List<Department>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetDepartmentsByDegreeCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<Department>>> Handle(GetDepartmentsByDegreeCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var departments = await _unitOfWork.FilterRepository.GetDepartmentsByDegreeAsync(request.DegreeId);
                return ApiResponse<List<Department>>.SuccessResponse(departments.ToList(), "Departments by degree retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<Department>>.ErrorResponse($"Error during fetching departments by degree: {ex.Message}");
            }
        }
    }

    public class GetActiveAcademicYearsCommandHandler : IRequestHandler<GetActiveAcademicYearsCommand, ApiResponse<List<AcademicYear>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetActiveAcademicYearsCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<AcademicYear>>> Handle(GetActiveAcademicYearsCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var academicYears = await _unitOfWork.FilterRepository.GetActiveAcademicYearsAsync();
                return ApiResponse<List<AcademicYear>>.SuccessResponse(academicYears.ToList(), "Active academic years retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<AcademicYear>>.ErrorResponse($"Error during fetching active academic years: {ex.Message}");
            }
        }
    }

    public class GetAcademicYearsByDegreeCommandHandler : IRequestHandler<GetAcademicYearsByDegreeCommand, ApiResponse<List<AcademicYear>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetAcademicYearsByDegreeCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<AcademicYear>>> Handle(GetAcademicYearsByDegreeCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var academicYears = await _unitOfWork.FilterRepository.GetAcademicYearsByDegreeAsync(request.DegreeId);
                return ApiResponse<List<AcademicYear>>.SuccessResponse(academicYears.ToList(), "Academic years by degree retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<AcademicYear>>.ErrorResponse($"Error during fetching academic years by degree: {ex.Message}");
            }
        }
    }

    public class GetAcademicYearsByDegreeAndDepartmentCommandHandler : IRequestHandler<GetAcademicYearsByDegreeAndDepartmentCommand, ApiResponse<List<AcademicYear>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetAcademicYearsByDegreeAndDepartmentCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<AcademicYear>>> Handle(GetAcademicYearsByDegreeAndDepartmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Since academic years are now linked to degree (level_id) only, we use the degree-based method
                var academicYears = await _unitOfWork.FilterRepository.GetAcademicYearsByDegreeAsync(request.DegreeId);
                return ApiResponse<List<AcademicYear>>.SuccessResponse(academicYears.ToList(), "Academic years by degree retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<AcademicYear>>.ErrorResponse($"Error during fetching academic years by degree: {ex.Message}");
            }
        }
    }

    public class GetFilteredSemestersCommandHandler : IRequestHandler<GetFilteredSemestersCommand, ApiResponse<List<Semester>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetFilteredSemestersCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<Semester>>> Handle(GetFilteredSemestersCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var semesters = await _unitOfWork.FilterRepository.GetSemestersByFiltersAsync(
                    request.DegreeId,
                    request.DepartmentId,
                    request.AcademicYearId);
                return ApiResponse<List<Semester>>.SuccessResponse(semesters.ToList(), "Filtered semesters retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<Semester>>.ErrorResponse($"Error during fetching filtered semesters: {ex.Message}");
            }
        }
    }

    public class GetFilteredCoursesCommandHandler : IRequestHandler<GetFilteredCoursesCommand, ApiResponse<List<Course>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetFilteredCoursesCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<Course>>> Handle(GetFilteredCoursesCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var courses = await _unitOfWork.FilterRepository.GetCoursesByFiltersAsync(
                    request.DegreeId,
                    request.DepartmentId,
                    request.AcademicYearId,
                    request.SemesterId);
                return ApiResponse<List<Course>>.SuccessResponse(courses.ToList(), "Filtered courses retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<Course>>.ErrorResponse($"Error during fetching filtered courses: {ex.Message}");
            }
        }
    }

    public class GetSyllabiByDepartmentCommandHandler : IRequestHandler<GetSyllabiByDepartmentCommand, ApiResponse<List<object>>>
    {
        private readonly IUnitOfWork _unitOfWork;

        public GetSyllabiByDepartmentCommandHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ApiResponse<List<object>>> Handle(GetSyllabiByDepartmentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var syllabi = await _unitOfWork.FilterRepository.GetSyllabiByDepartmentAsync(request.DepartmentId);
                return ApiResponse<List<object>>.SuccessResponse(syllabi.ToList(), "Syllabi by department retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse<List<object>>.ErrorResponse($"Error during fetching syllabi by department: {ex.Message}");
            }
        }
    }
}
