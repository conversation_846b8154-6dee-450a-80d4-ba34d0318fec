export interface Degree {
  id: number;
  name: string;
  active: boolean;
  createdDate?: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Department {
  id: number;
  name: string;
  code: string;
  description?: string;
  headOfDepartment?: string;
  email?: string;
  phone?: string;
  establishedYear?: number;
  programsOffered?: string;
  accreditation?: string;
  status: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  levelId: number;
}

export interface AcademicYear {
  id: number;
  name: string;
  code: string;
  startYear: number;
  endYear: number;
  levelId: number;
  status: string;
  description?: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Course {
  id: number;
  name: string;
  code: string;
  description?: string;
  credits: number;
  courseType: string;
  durationWeeks: number;
  maxCapacity: number;
  status: string;
  prerequisites?: string;
  learningObjectives?: string;
  learningOutcomes?: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Semester {
  id: number;
  name: string;
  code: string;
  departmentId: number;
  courseId?: number[];
  startDate: string;
  endDate: string;
  durationWeeks: number;
  totalStudents: number;
  status: string;
  description?: string;
  examScheduled: boolean;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  academicYearId: number;
  levelId: number;
}

export interface AcademicYear {
  id: number;
  name: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Document {
  id: string;
  title: string;
  type: 'Syllabus' | 'Lesson' | 'Session';
  status: string;
  url?: string;
  createdDate?: string;
  modifiedDate?: string;
  facultyName?: string;
  isActive: boolean;

  // Additional properties for different types
  credits?: number; // For Syllabus
  durationWeeks?: number; // For Syllabus
  durationMinutes?: number; // For LessonPlan and Session
  numberOfSessions?: number; // For LessonPlan
  scheduledDate?: string; // For LessonPlan
  sessionDate?: string; // For Session
  sessionTime?: string; // For Session
  teachingMethod?: string; // For Session
  instructor?: string; // For Session

  // Filter-related properties
  courseId?: number;
  semesterId?: number;
  academicYearId?: number;
  parentId?: string; // For linking lessons to syllabus, sessions to lessons

  // Legacy properties for backward compatibility
  fileSize?: string;
  uploadDate?: string;
  description?: string;
  lessonNumber?: number;
  sessionNumber?: number;
  lessonTitle?: string;
  sessionTitle?: string;
}

export interface FilterState {
  degree: number | '';
  department: number | '';
  course: number | '';
  semester: number | '';
  academicYear: number | '';
}

export interface HierarchicalDocument {
  id: string;
  title: string;
  type: 'Syllabus' | 'Lesson' | 'Session';
  document: Document;
  children?: HierarchicalDocument[];
  expanded?: boolean;
}