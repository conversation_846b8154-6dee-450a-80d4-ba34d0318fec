using Microsoft.AspNetCore.Mvc;
using WorkFlowViewMgmt.Application.Features.Filter;

namespace WorkFlowViewMgmt.WebAPI.Controllers
{
    [Route("api/filter")]
    public class FilterController : BaseApiController
    {
        [HttpGet("degrees/active")]
        public async Task<IActionResult> GetActiveDegrees()
        {
            var result = await Mediator.Send(new GetActiveDegreesCommand());
            return Ok(result);
        }

        [HttpGet("departments/active")]
        public async Task<IActionResult> GetActiveDepartments()
        {
            var result = await Mediator.Send(new GetActiveDepartmentsCommand());
            return Ok(result);
        }

        [HttpGet("departments/by-degree/{degreeId}")]
        public async Task<IActionResult> GetDepartmentsByDegree(int degreeId)
        {
            var result = await Mediator.Send(new GetDepartmentsByDegreeCommand(degreeId));
            return Ok(result);
        }

        [HttpGet("academic-years/active")]
        public async Task<IActionResult> GetActiveAcademicYears()
        {
            var result = await Mediator.Send(new GetActiveAcademicYearsCommand());
            return Ok(result);
        }

        [HttpGet("academic-years/by-degree/{degreeId}")]
        public async Task<IActionResult> GetAcademicYearsByDegree(int degreeId)
        {
            var result = await Mediator.Send(new GetAcademicYearsByDegreeCommand(degreeId));
            return Ok(result);
        }

        [HttpGet("academic-years/by-degree-department")]
        public async Task<IActionResult> GetAcademicYearsByDegreeAndDepartment(
            [FromQuery] int degreeId,
            [FromQuery] int departmentId)
        {
            var result = await Mediator.Send(new GetAcademicYearsByDegreeCommand(degreeId));
            return Ok(result);
        }

        [HttpGet("semesters")]
        public async Task<IActionResult> GetFilteredSemesters(
            [FromQuery] int? degreeId = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] int? academicYearId = null)
        {
            var result = await Mediator.Send(new GetFilteredSemestersCommand(degreeId, departmentId, academicYearId));
            return Ok(result);
        }

        [HttpGet("courses")]
        public async Task<IActionResult> GetFilteredCourses(
            [FromQuery] int? degreeId = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] int? academicYearId = null,
            [FromQuery] int? semesterId = null)
        {
            var result = await Mediator.Send(new GetFilteredCoursesCommand(degreeId, departmentId, academicYearId, semesterId));
            return Ok(result);
        }

        [HttpGet("syllabi/by-department/{departmentId}")]
        public async Task<IActionResult> GetSyllabiByDepartment(int departmentId)
        {
            var result = await Mediator.Send(new GetSyllabiByDepartmentCommand(departmentId));
            return Ok(result);
        }
    }
}
