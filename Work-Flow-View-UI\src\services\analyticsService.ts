// Analytics Service for tracking user behavior and document usage
export interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  userId?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface DocumentAnalytics {
  documentId: string;
  title: string;
  type: string;
  views: number;
  downloads: number;
  lastAccessed: string;
  averageViewTime: number;
  popularityScore: number;
}

export interface UserAnalytics {
  userId: string;
  totalSessions: number;
  totalDocumentsViewed: number;
  totalDownloads: number;
  averageSessionDuration: number;
  favoriteDocumentTypes: string[];
  lastActivity: string;
  preferences: Record<string, any>;
}

class AnalyticsService {
  private static instance: AnalyticsService;
  private events: AnalyticsEvent[] = [];
  private sessionStart: number = Date.now();
  private currentUserId: string | null = null;

  private constructor() {
    this.loadStoredEvents();
    this.setupBeforeUnload();
  }

  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  public setUserId(userId: string) {
    this.currentUserId = userId;
  }

  public track(event: string, category: string, action: string, label?: string, value?: number, metadata?: Record<string, any>) {
    const analyticsEvent: AnalyticsEvent = {
      event,
      category,
      action,
      label,
      value,
      userId: this.currentUserId || 'anonymous',
      timestamp: Date.now(),
      metadata
    };

    this.events.push(analyticsEvent);
    this.saveEvents();

    // Send to external analytics if configured
    this.sendToExternalAnalytics(analyticsEvent);
  }

  // Document-specific tracking methods
  public trackDocumentView(documentId: string, title: string, type: string) {
    this.track('document_view', 'document', 'view', documentId, undefined, {
      title,
      type,
      viewTime: Date.now()
    });
  }

  public trackDocumentDownload(documentId: string, title: string, type: string) {
    this.track('document_download', 'document', 'download', documentId, undefined, {
      title,
      type
    });
  }

  public trackSearch(query: string, resultsCount: number, filters?: any) {
    this.track('search', 'search', 'query', query, resultsCount, {
      filters,
      hasResults: resultsCount > 0
    });
  }

  public trackFilterUsage(filterType: string, filterValue: string) {
    this.track('filter_usage', 'filter', 'apply', filterType, undefined, {
      value: filterValue
    });
  }

  public trackBulkAction(action: string, documentCount: number) {
    this.track('bulk_action', 'bulk', action, undefined, documentCount);
  }

  public trackUserPreferenceChange(preference: string, value: any) {
    this.track('preference_change', 'user', 'preference', preference, undefined, {
      value
    });
  }

  public trackError(error: string, context?: string) {
    this.track('error', 'error', 'occurred', error, undefined, {
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  }

  public trackPerformance(metric: string, value: number, context?: string) {
    this.track('performance', 'performance', metric, context, value);
  }

  // Analytics data retrieval methods
  public getDocumentAnalytics(): DocumentAnalytics[] {
    const documentStats = new Map<string, any>();

    this.events.forEach(event => {
      if (event.category === 'document' && event.label) {
        const docId = event.label;
        if (!documentStats.has(docId)) {
          documentStats.set(docId, {
            documentId: docId,
            title: event.metadata?.title || 'Unknown',
            type: event.metadata?.type || 'Unknown',
            views: 0,
            downloads: 0,
            lastAccessed: event.timestamp,
            viewTimes: [],
            popularityScore: 0
          });
        }

        const stats = documentStats.get(docId);
        
        if (event.action === 'view') {
          stats.views++;
          stats.lastAccessed = Math.max(stats.lastAccessed, event.timestamp);
          if (event.metadata?.viewTime) {
            stats.viewTimes.push(event.metadata.viewTime);
          }
        } else if (event.action === 'download') {
          stats.downloads++;
        }
      }
    });

    return Array.from(documentStats.values()).map(stats => ({
      ...stats,
      lastAccessed: new Date(stats.lastAccessed).toISOString(),
      averageViewTime: stats.viewTimes.length > 0 
        ? stats.viewTimes.reduce((a: number, b: number) => a + b, 0) / stats.viewTimes.length 
        : 0,
      popularityScore: (stats.views * 1) + (stats.downloads * 2)
    }));
  }

  public getUserAnalytics(): UserAnalytics | null {
    if (!this.currentUserId) return null;

    const userEvents = this.events.filter(e => e.userId === this.currentUserId);
    const documentViews = userEvents.filter(e => e.category === 'document' && e.action === 'view');
    const downloads = userEvents.filter(e => e.category === 'document' && e.action === 'download');
    
    const documentTypes = documentViews.map(e => e.metadata?.type).filter(Boolean);
    const typeFrequency = documentTypes.reduce((acc: Record<string, number>, type: string) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const favoriteTypes = Object.entries(typeFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([type]) => type);

    return {
      userId: this.currentUserId,
      totalSessions: this.getSessionCount(),
      totalDocumentsViewed: new Set(documentViews.map(e => e.label)).size,
      totalDownloads: downloads.length,
      averageSessionDuration: this.getAverageSessionDuration(),
      favoriteDocumentTypes: favoriteTypes,
      lastActivity: new Date(Math.max(...userEvents.map(e => e.timestamp))).toISOString(),
      preferences: this.getUserPreferences()
    };
  }

  public getPopularDocuments(limit: number = 10): DocumentAnalytics[] {
    return this.getDocumentAnalytics()
      .sort((a, b) => b.popularityScore - a.popularityScore)
      .slice(0, limit);
  }

  public getSearchAnalytics() {
    const searchEvents = this.events.filter(e => e.category === 'search');
    const queries = searchEvents.map(e => e.label).filter(Boolean);
    const queryFrequency = queries.reduce((acc: Record<string, number>, query: string) => {
      acc[query] = (acc[query] || 0) + 1;
      return acc;
    }, {});

    return {
      totalSearches: searchEvents.length,
      uniqueQueries: Object.keys(queryFrequency).length,
      popularQueries: Object.entries(queryFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([query, count]) => ({ query, count })),
      averageResultsPerSearch: searchEvents.reduce((sum, e) => sum + (e.value || 0), 0) / searchEvents.length || 0
    };
  }

  private loadStoredEvents() {
    try {
      const stored = localStorage.getItem('analyticsEvents');
      if (stored) {
        this.events = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load stored analytics events:', error);
    }
  }

  private saveEvents() {
    try {
      // Keep only last 1000 events to prevent storage bloat
      const eventsToStore = this.events.slice(-1000);
      localStorage.setItem('analyticsEvents', JSON.stringify(eventsToStore));
    } catch (error) {
      console.error('Failed to save analytics events:', error);
    }
  }

  private sendToExternalAnalytics(event: AnalyticsEvent) {
    // Send to Google Analytics if available
    if (typeof (window as any).gtag !== 'undefined') {
      const gtag = (window as any).gtag;
      gtag('event', event.action, {
        event_category: event.category,
        event_label: event.label,
        value: event.value,
        custom_map: event.metadata
      });
    }

    // Send to other analytics services as needed
    // Example: Mixpanel, Amplitude, etc.
  }

  private setupBeforeUnload() {
    window.addEventListener('beforeunload', () => {
      const sessionDuration = Date.now() - this.sessionStart;
      this.track('session_end', 'session', 'end', undefined, sessionDuration);
    });
  }

  private getSessionCount(): number {
    const sessions = this.events.filter(e => e.event === 'session_start');
    return sessions.length;
  }

  private getAverageSessionDuration(): number {
    const sessionEnds = this.events.filter(e => e.event === 'session_end');
    if (sessionEnds.length === 0) return 0;
    
    const totalDuration = sessionEnds.reduce((sum, e) => sum + (e.value || 0), 0);
    return totalDuration / sessionEnds.length;
  }

  private getUserPreferences(): Record<string, any> {
    const prefEvents = this.events.filter(e => e.category === 'user' && e.action === 'preference');
    const preferences: Record<string, any> = {};
    
    prefEvents.forEach(e => {
      if (e.label) {
        preferences[e.label] = e.metadata?.value;
      }
    });
    
    return preferences;
  }
}

export const analyticsService = AnalyticsService.getInstance();
