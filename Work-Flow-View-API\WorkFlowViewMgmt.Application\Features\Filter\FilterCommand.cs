using MediatR;
using WorkFlowViewMgmt.Application.Common.Models;
using WorkFlowViewMgmt.Domain.Entities.Degree;
using WorkFlowViewMgmt.Domain.Entities.Filter;

namespace WorkFlowViewMgmt.Application.Features.Filter
{
    // Get all active degrees
    public record GetActiveDegreesCommand() : IRequest<ApiResponse<List<Degree>>>;

    // Get all active departments
    public record GetActiveDepartmentsCommand() : IRequest<ApiResponse<List<Department>>>;

    // Get departments filtered by degree
    public record GetDepartmentsByDegreeCommand(int DegreeId) : IRequest<ApiResponse<List<Department>>>;

    // Get all active academic years
    public record GetActiveAcademicYearsCommand() : IRequest<ApiResponse<List<AcademicYear>>>;

    // Get academic years filtered by degree (level_id)
    public record GetAcademicYearsByDegreeCommand(int DegreeId) : IRequest<ApiResponse<List<AcademicYear>>>;

    // Get academic years filtered by degree and department (kept for backward compatibility)
    public record GetAcademicYearsByDegreeAndDepartmentCommand(int DegreeId, int DepartmentId) : IRequest<ApiResponse<List<AcademicYear>>>;

    // Get semesters filtered by degree, department, and academic year
    public record GetFilteredSemestersCommand(
        int? DegreeId = null,
        int? DepartmentId = null,
        int? AcademicYearId = null
    ) : IRequest<ApiResponse<List<Semester>>>;

    // Get courses filtered by degree, department, academic year, and semester
    public record GetFilteredCoursesCommand(
        int? DegreeId = null,
        int? DepartmentId = null,
        int? AcademicYearId = null,
        int? SemesterId = null
    ) : IRequest<ApiResponse<List<Course>>>;

    // Get syllabi by department only
    public record GetSyllabiByDepartmentCommand(int DepartmentId) : IRequest<ApiResponse<List<object>>>;
}
