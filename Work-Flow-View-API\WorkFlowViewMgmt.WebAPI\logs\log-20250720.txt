2025-07-20 23:57:39.020 +05:30 [INF] Starting WorkFlow View Management API
2025-07-20 23:57:39.257 +05:30 [WRN] The WebRootPath was not found: E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\wwwroot. Static files may be unavailable.
2025-07-20 23:57:39.313 +05:30 [INF] Now listening on: http://localhost:5000
2025-07-20 23:57:39.318 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 23:57:39.319 +05:30 [INF] Hosting environment: Development
2025-07-20 23:57:39.319 +05:30 [INF] Content root path: E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI
2025-07-20 23:57:39.719 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/_framework/aspnetcore-browser-refresh.js - null null
2025-07-20 23:57:39.866 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/_framework/aspnetcore-browser-refresh.js - 200 13732 application/javascript; charset=utf-8 150.019ms
2025-07-20 23:57:39.914 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/v1/swagger.json - null null
2025-07-20 23:57:40.170 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 256.4103ms
2025-07-20 23:59:40.811 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - null null
2025-07-20 23:59:40.844 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:40.847 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:40.881 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:47.193 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 6309.6011ms
2025-07-20 23:59:47.195 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:47.195 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitDisposeCache(ServiceCallSite transientCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitDisposeCache(ServiceCallSite transientCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-20 23:59:48.926 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - 500 null application/json 8114.7984ms
2025-07-20 23:59:48.928 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - null null
2025-07-20 23:59:48.930 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - null null
2025-07-20 23:59:48.930 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:48.930 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:48.930 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:48.930 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:48.930 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:48.930 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:52.997 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4066.728ms
2025-07-20 23:59:52.997 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4066.7457ms
2025-07-20 23:59:52.998 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:52.998 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:52.998 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-20 23:59:52.998 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-20 23:59:53.003 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - 500 null application/json 4073.2544ms
2025-07-20 23:59:53.007 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - 500 null application/json 4079.0907ms
2025-07-20 23:59:53.007 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - null null
2025-07-20 23:59:53.008 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:53.008 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - null null
2025-07-20 23:59:53.008 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:53.009 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:53.009 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:53.009 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:53.009 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:53.010 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - null null
2025-07-20 23:59:53.011 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:53.011 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:53.011 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:57.074 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4064.7832ms
2025-07-20 23:59:57.074 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:57.074 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-20 23:59:57.081 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - 500 null application/json 4073.607ms
2025-07-20 23:59:57.084 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - null null
2025-07-20 23:59:57.084 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:57.084 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:57.085 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:57.089 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4078.0741ms
2025-07-20 23:59:57.089 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4079.8771ms
2025-07-20 23:59:57.089 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:57.089 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:57.089 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-20 23:59:57.089 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-20 23:59:57.092 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - 500 null application/json 4082.0233ms
2025-07-20 23:59:57.097 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - null null
2025-07-20 23:59:57.097 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - 500 null application/json 4088.4531ms
2025-07-20 23:59:57.097 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:57.097 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:57.097 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:57.099 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - null null
2025-07-20 23:59:57.099 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:57.099 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:57.100 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-20 23:59:57.101 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - null null
2025-07-20 23:59:57.101 +05:30 [INF] CORS policy execution successful.
2025-07-20 23:59:57.101 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-20 23:59:57.101 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
