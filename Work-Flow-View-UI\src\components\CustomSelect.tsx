import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check, Search } from 'lucide-react';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  description?: string;
}

interface CustomSelectProps {
  options: SelectOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  label?: string;
  error?: string;
  required?: boolean;
  className?: string;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select an option...',
  disabled = false,
  searchable = false,
  clearable = false,
  size = 'md',
  variant = 'default',
  label,
  error,
  required = false,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const selectedOption = options.find(option => option.value === value);
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside and handle mobile issues
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    const handleResize = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
      document.addEventListener('scroll', handleScroll, true);
      document.addEventListener('resize', handleResize);
      
      // Prevent body scroll on mobile when dropdown is open
      document.body.style.overflow = 'hidden';
      document.body.classList.add('dropdown-open');
    } else {
      // Restore body scroll
      document.body.style.overflow = '';
      document.body.classList.remove('dropdown-open');
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
      document.removeEventListener('scroll', handleScroll, true);
      document.removeEventListener('resize', handleResize);
      document.body.style.overflow = '';
      document.body.classList.remove('dropdown-open');
    };
  }, [isOpen]);

  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setSearchTerm('');
    setHighlightedIndex(-1);
    
    // Prevent any further event propagation
    setTimeout(() => {
      document.activeElement?.blur();
    }, 100);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange('');
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 text-sm';
      case 'lg':
        return 'px-4 py-4 text-lg';
      default:
        return 'px-4 py-3 text-base';
    }
  };

  const getVariantClasses = () => {
    const baseClasses = 'w-full border rounded-xl transition-all duration-200 focus-within:outline-none';
    
    switch (variant) {
      case 'outline':
        return `${baseClasses} bg-transparent border-gray-300 hover:border-gray-400 focus-within:border-blue-500 focus-within:ring-4 focus-within:ring-blue-500/20`;
      case 'ghost':
        return `${baseClasses} bg-gray-50/50 border-transparent hover:bg-gray-100/50 focus-within:bg-white focus-within:border-gray-200 focus-within:ring-4 focus-within:ring-blue-500/20`;
      default:
        return `${baseClasses} bg-white/90 backdrop-blur-sm border-gray-200 hover:border-gray-300 focus-within:border-blue-500 focus-within:ring-4 focus-within:ring-blue-500/20 shadow-lg hover:shadow-xl`;
    }
  };

  return (
    <div
      className={`relative dropdown-container ${isOpen ? 'open' : ''} ${className}`}
      style={{
        isolation: 'isolate',
        zIndex: isOpen ? 1000 : 'auto'
      }}
    >
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div
        ref={selectRef}
        className={`
          ${getSizeClasses()}
          ${getVariantClasses()}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'border-red-500 focus-within:border-red-500 focus-within:ring-red-500/20' : ''}
          relative flex items-center justify-between
        `}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled) {
            setIsOpen(!isOpen);
          }
        }}
        onTouchEnd={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled) {
            setIsOpen(!isOpen);
          }
        }}
        style={{
          touchAction: 'manipulation',
          WebkitTapHighlightColor: 'transparent'
        }}
      >
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          {selectedOption?.icon && (
            <div className="flex-shrink-0">
              {selectedOption.icon}
            </div>
          )}
          <div className="flex-1 min-w-0">
            {selectedOption ? (
              <div>
                <div className="font-medium text-gray-900 truncate">
                  {selectedOption.label}
                </div>
                {selectedOption.description && (
                  <div className="text-xs text-gray-500 truncate">
                    {selectedOption.description}
                  </div>
                )}
              </div>
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2 flex-shrink-0">
          {clearable && selectedOption && (
            <button
              onClick={handleClear}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              type="button"
            >
              <span className="text-gray-400 hover:text-gray-600">✕</span>
            </button>
          )}
          <ChevronDown 
            className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`} 
          />
        </div>

        {/* Dropdown */}
        {isOpen && (
          <>
            {/* Backdrop to prevent clicks on other elements */}
            <div
              className="fixed inset-0 z-[9998] bg-transparent"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsOpen(false);
              }}
            />
            <div
              className="dropdown-menu absolute left-0 right-0 bg-white border border-gray-200 rounded-xl shadow-2xl z-[9999] max-h-64 overflow-hidden"
              style={{
                position: 'absolute',
                top: '100%',
                marginTop: '0.5rem',
                zIndex: 9999,
                touchAction: 'manipulation',
                WebkitOverflowScrolling: 'touch',
                minWidth: '100%',
                maxHeight: window.innerHeight < 600 ? '40vh' : '16rem'
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              {searchable && (
                <div className="p-3 border-b border-gray-100">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      ref={searchInputRef}
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="Search options..."
                      className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                  </div>
                </div>
              )}
              
              <div className="max-h-48 overflow-y-auto">
                {filteredOptions.length === 0 ? (
                  <div className="px-4 py-3 text-sm text-gray-500 text-center">
                    No options found
                  </div>
                ) : (
                  filteredOptions.map((option, index) => (
                    <button
                      key={option.value}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (!option.disabled) {
                          handleOptionSelect(option.value);
                        }
                      }}
                      onTouchEnd={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (!option.disabled) {
                          handleOptionSelect(option.value);
                        }
                      }}
                      className={`
                        dropdown-option w-full px-4 py-3 text-left flex items-center space-x-3 transition-colors
                        ${option.disabled
                          ? 'opacity-50 cursor-not-allowed'
                          : 'hover:bg-blue-50 active:bg-blue-100 cursor-pointer'
                        }
                        ${index === highlightedIndex ? 'bg-blue-50' : ''}
                        ${option.value === value ? 'bg-blue-100 text-blue-700' : 'text-gray-900'}
                      `}
                      disabled={option.disabled}
                      type="button"
                      style={{
                        touchAction: 'manipulation',
                        WebkitTapHighlightColor: 'transparent'
                      }}
                    >
                      {option.icon && (
                        <div className="flex-shrink-0">
                          {option.icon}
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">
                          {option.label}
                        </div>
                        {option.description && (
                          <div className="text-xs text-gray-500 truncate">
                            {option.description}
                          </div>
                        )}
                      </div>
                      {option.value === value && (
                        <Check className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      )}
                    </button>
                  ))
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {error && (
        <p className="mt-2 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default CustomSelect;
