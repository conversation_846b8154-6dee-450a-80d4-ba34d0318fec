namespace WorkFlowViewMgmt.WebAPI.Models
{
    public class FileResult
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public byte[] Content { get; set; } = Array.Empty<byte>();
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public string RelativePath { get; set; } = string.Empty;
        public string FullPath { get; set; } = string.Empty;
        public bool Exists { get; set; }
    }

    public class FileInfoDto
    {
        public string Name { get; set; } = string.Empty;
        public string Extension { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public string RelativePath { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string FolderType { get; set; } = string.Empty;
    }

    public class FolderInfoDto
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public int FileCount { get; set; }
        public long TotalSize { get; set; }
    }
}
