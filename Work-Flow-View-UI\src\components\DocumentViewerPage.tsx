import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { apiConfig } from '../services/config';

interface DocumentData {
  id: string;
  title: string;
  type: string;
  status: string;
  url: string;
  facultyName?: string;
  createdDate?: string;
  credits?: number;
  durationMinutes?: number;
  durationWeeks?: number;
  numberOfSessions?: number;
  scheduledDate?: string;
  sessionDate?: string;
  sessionTime?: string;
  teachingMethod?: string;
  instructor?: string;
  modifiedDate?: string;
}

const DocumentViewerPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [documentData, setDocumentData] = useState<DocumentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingProgress, setLoadingProgress] = useState(0);

  useEffect(() => {
    // Simulate loading progress
    const progressInterval = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 100);

    // Try to get document URL directly from URL parameters first
    const directUrl = searchParams.get('url');
    const title = searchParams.get('title');
    const type = searchParams.get('type');
    const id = searchParams.get('id');

    if (directUrl && title && type && id) {
      // Direct URL parameters approach
      console.log('Using direct URL parameters');
      console.log('Direct URL:', directUrl);

      setDocumentData({
        id,
        title,
        type,
        status: 'Active',
        url: directUrl,
        facultyName: searchParams.get('faculty') || undefined,
        createdDate: searchParams.get('created') || undefined
      });
      setLoadingProgress(100);
    } else {
      // Fallback to data parameter approach
      const docData = searchParams.get('data');

      if (docData) {
        try {
          const parsedData = JSON.parse(decodeURIComponent(docData));
          console.log('Parsed document data:', parsedData); // Debug log

          // Validate required fields
          if (!parsedData.id || !parsedData.title || !parsedData.url) {
            setError('Document information is incomplete. Please try opening the document again.');
          } else {
            setDocumentData(parsedData);
            setLoadingProgress(100);
          }
        } catch (err) {
          console.error('Error parsing document data:', err);
          setError('Unable to load document. Please try again or contact support if the problem persists.');
        }
      } else {
        setError('Document link is invalid. Please try opening the document from the main page.');
      }
    }

    setTimeout(() => {
      setLoading(false);
      clearInterval(progressInterval);
    }, 1000);

    return () => clearInterval(progressInterval);
  }, [searchParams]);

  // Removed color theming and formatting functions since we're using clean native browser view

  const generateDocumentEmbed = (documentUrl: string) => {
    if (!documentUrl) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          color: '#666',
          textAlign: 'center',
          padding: '2rem'
        }}>
          <div style={{ fontSize: '4rem', marginBottom: '1rem', opacity: 0.5 }}>❌</div>
          <h3>Document Not Available</h3>
          <p>We couldn't find this document. It may have been moved or deleted.</p>
          <button
            onClick={() => window.close()}
            style={{
              marginTop: '1rem',
              padding: '0.75rem 1.5rem',
              background: '#6B7280',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer'
            }}
          >
            Close Window
          </button>
        </div>
      );
    }

    const fileExtension = documentUrl.split('.').pop()?.toLowerCase();
    console.log('Document URL:', documentUrl, 'Extension:', fileExtension); // Debug log

    // Create a fallback component for when iframe fails to load
    const createFallbackView = (message: string) => (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        color: '#666',
        textAlign: 'center',
        padding: '2rem'
      }}>
        <div style={{ fontSize: '4rem', marginBottom: '1rem', opacity: 0.5 }}>📄</div>
        <h3>Document Preview Unavailable</h3>
        <p>{message}</p>
        <p>You can download the document to view it on your device.</p>
        <a
          href={documentUrl}
          download
          style={{
            marginTop: '1rem',
            padding: '0.75rem 1.5rem',
            background: 'linear-gradient(135deg, #3B82F6, #1E40AF)',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '8px',
            display: 'inline-block'
          }}
        >
          📥 Download Document
        </a>
      </div>
    );

    // Try direct PDF viewing first
    if (fileExtension === 'pdf') {
      return (
        <div style={{ width: '100%', height: '100%' }}>
          <iframe
            src={documentUrl}
            style={{ width: '100%', height: '100%', border: 'none' }}
            title="PDF Document"
            onLoad={() => console.log('PDF loaded successfully')}
            onError={() => console.error('PDF failed to load')}
          />
        </div>
      );
    }

    // For Office documents, try multiple viewers
    else if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(fileExtension || '')) {
      // Try Google Docs Viewer first
      const googleViewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(documentUrl)}&embedded=true`;

      return (
        <div style={{ width: '100%', height: '100%' }}>
          <iframe
            src={googleViewerUrl}
            style={{ width: '100%', height: '100%', border: 'none' }}
            title="Document Viewer"
            onLoad={() => console.log('Document loaded successfully')}
            onError={() => {
              console.error('Google Viewer failed, trying Office Online');
              // Could implement fallback to Office Online viewer here
            }}
          />
        </div>
      );
    }

    // For text files
    else if (['txt', 'text', 'md'].includes(fileExtension || '')) {
      return (
        <div style={{ width: '100%', height: '100%' }}>
          <iframe
            src={documentUrl}
            style={{ width: '100%', height: '100%', border: 'none', background: 'white' }}
            title="Text Document"
            onLoad={() => console.log('Text file loaded successfully')}
            onError={() => console.error('Text file failed to load')}
          />
        </div>
      );
    }

    // For unsupported file types
    else {
      return createFallbackView(`This ${fileExtension?.toUpperCase() || 'file'} type cannot be previewed in the browser.`);
    }
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'
      }}>
        <div style={{
          textAlign: 'center',
          background: 'white',
          padding: '3rem 2rem',
          borderRadius: '16px',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            display: 'inline-block',
            width: '48px',
            height: '48px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            marginBottom: '1.5rem'
          }}></div>
          <h3 style={{
            fontSize: '1.25rem',
            fontWeight: '600',
            color: '#1f2937',
            marginBottom: '0.5rem'
          }}>
            Loading Document
          </h3>
          <p style={{ color: '#6b7280', fontSize: '0.875rem', marginBottom: '1rem' }}>
            Please wait while we prepare your document...
          </p>

          {/* Progress Bar */}
          <div style={{
            width: '200px',
            height: '4px',
            background: '#e5e7eb',
            borderRadius: '2px',
            margin: '0 auto',
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${loadingProgress}%`,
              height: '100%',
              background: 'linear-gradient(90deg, #3b82f6, #1d4ed8)',
              borderRadius: '2px',
              transition: 'width 0.3s ease'
            }} />
          </div>
          <p style={{
            color: '#9ca3af',
            fontSize: '0.75rem',
            marginTop: '0.5rem'
          }}>
            {loadingProgress}%
          </p>
        </div>
      </div>
    );
  }

  if (error || !documentData) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        padding: '2rem'
      }}>
        <div style={{
          textAlign: 'center',
          background: 'white',
          padding: '3rem 2rem',
          borderRadius: '16px',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',
          maxWidth: '500px',
          width: '100%'
        }}>
          <div style={{
            fontSize: '4rem',
            marginBottom: '1.5rem',
            background: 'linear-gradient(135deg, #ef4444, #dc2626)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            📄
          </div>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#1f2937',
            marginBottom: '1rem'
          }}>
            Oops! Something went wrong
          </h2>
          <p style={{
            color: '#6b7280',
            marginBottom: '2rem',
            lineHeight: '1.6'
          }}>
            {error || 'We couldn\'t load this document. Please try again.'}
          </p>

          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '0.75rem 1.5rem',
                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: '600',
                fontSize: '0.875rem'
              }}
            >
              🔄 Try Again
            </button>

            <button
              onClick={() => window.close()}
              style={{
                padding: '0.75rem 1.5rem',
                background: '#f9fafb',
                color: '#6b7280',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: '600',
                fontSize: '0.875rem'
              }}
            >
              ✕ Close
            </button>
          </div>

          <p style={{
            fontSize: '0.75rem',
            color: '#9ca3af',
            marginTop: '2rem'
          }}>
            If this problem continues, please contact support
          </p>
        </div>
      </div>
    );
  }

  // Debug logging
  console.log('Document data:', documentData);
  console.log('Document URL from data:', documentData.url);
  console.log('Current URL search params:', window.location.search);

  const fullDocumentUrl = apiConfig.getDocumentUrl(documentData.url);
  console.log('Full document URL after apiConfig:', fullDocumentUrl);

  // Additional check - if URL is still relative or empty, try to construct it manually
  let finalDocumentUrl = fullDocumentUrl;
  if (!fullDocumentUrl || (!fullDocumentUrl.startsWith('http') && !fullDocumentUrl.startsWith('blob:'))) {
    // If we still don't have a proper URL, try different approaches
    if (documentData.url) {
      if (documentData.url.startsWith('/')) {
        // Relative URL - prepend base URL
        finalDocumentUrl = `${apiConfig.baseUrl.replace('/api', '')}${documentData.url}`;
      } else if (!documentData.url.startsWith('http')) {
        // No leading slash - add it
        finalDocumentUrl = `${apiConfig.baseUrl.replace('/api', '')}/${documentData.url}`;
      } else {
        finalDocumentUrl = documentData.url;
      }
    }
  }

  // Final fallback - if we still don't have a working URL, use a test document
  if (!finalDocumentUrl || finalDocumentUrl === 'undefined' || finalDocumentUrl === 'null') {
    console.warn('No valid document URL found, using test document');
    finalDocumentUrl = `${window.location.origin}/test-document.txt`;
  }

  console.log('Final document URL:', finalDocumentUrl);

  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      background: '#ffffff',
      height: '100vh',
      width: '100vw',
      display: 'flex',
      flexDirection: 'column',
      position: 'fixed',
      top: 0,
      left: 0,
      zIndex: 9999
    }}>
      {/* Clean Document Container - Full Screen without any headers */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: '#ffffff'
      }}>
        {generateDocumentEmbed(finalDocumentUrl)}
      </div>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default DocumentViewerPage;
