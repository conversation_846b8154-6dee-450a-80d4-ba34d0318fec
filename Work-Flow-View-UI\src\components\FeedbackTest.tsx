import React, { useState } from 'react';
import { feedbackService } from '../services/feedbackService';

const FeedbackTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testFeedbackService = async () => {
    setLoading(true);
    setTestResults([]);
    
    addResult('Starting feedback service tests...');

    // Test 1: Submit feedback
    try {
      addResult('Test 1: Submitting test feedback...');
      const submitResult = await feedbackService.submitFeedback(
        'test-lesson-id',
        'This is a test feedback',
        '<EMAIL>',
        'test_general',
        5
      );
      addResult(`Submit feedback result: ${submitResult ? 'SUCCESS' : 'FAILED'}`);
    } catch (error) {
      addResult(`Submit feedback error: ${error}`);
    }

    // Test 2: Get feedback stats
    try {
      addResult('Test 2: Getting feedback stats...');
      const stats = await feedbackService.getLessonFeedbackStats('test-lesson-id');
      addResult(`Get stats result: ${stats ? 'SUCCESS - ' + JSON.stringify(stats) : 'NO DATA'}`);
    } catch (error) {
      addResult(`Get stats error: ${error}`);
    }

    // Test 3: Get feedback list
    try {
      addResult('Test 3: Getting feedback list...');
      const feedback = await feedbackService.getLessonFeedback('test-lesson-id');
      addResult(`Get feedback result: ${feedback.length > 0 ? `SUCCESS - ${feedback.length} items` : 'NO DATA'}`);
    } catch (error) {
      addResult(`Get feedback error: ${error}`);
    }

    addResult('Tests completed!');
    setLoading(false);
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md">
      <h3 className="font-bold text-lg mb-2">Feedback Service Test</h3>
      
      <button
        onClick={testFeedbackService}
        disabled={loading}
        className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 mb-4"
      >
        {loading ? 'Testing...' : 'Run Tests'}
      </button>

      <div className="max-h-64 overflow-y-auto">
        {testResults.map((result, index) => (
          <div key={index} className="text-xs text-gray-600 mb-1 font-mono">
            {result}
          </div>
        ))}
      </div>
    </div>
  );
};

export default FeedbackTest;
