using System.ComponentModel.DataAnnotations;

namespace WorkFlowViewMgmt.Domain.Entities.Filter
{
    public class Department : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;

        public string? Description { get; set; }

        [MaxLength(100)]
        public string? HeadOfDepartment { get; set; }

        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        public int? EstablishedYear { get; set; }

        public string? ProgramsOffered { get; set; }

        public string? Accreditation { get; set; }

        [MaxLength(20)]
        public string Status { get; set; } = "Active";

        public bool EmailNotify { get; set; } = false;

        public bool SmsNotify { get; set; } = false;

        public bool InAppNotify { get; set; } = false;

        [MaxLength(20)]
        public string? DigestFrequency { get; set; }

        public int LevelId { get; set; } = 1;
    }
}
