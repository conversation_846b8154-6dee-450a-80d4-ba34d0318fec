import { useState, useCallback, useMemo } from 'react';
import { Document } from '../types';

export function useBulkSelection(documents: Document[]) {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  const selectedDocuments = useMemo(() => {
    return documents.filter(doc => selectedIds.has(doc.id));
  }, [documents, selectedIds]);

  const isSelected = useCallback((documentId: string) => {
    return selectedIds.has(documentId);
  }, [selectedIds]);

  const toggleSelection = useCallback((documentId: string) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback(() => {
    setSelectedIds(new Set(documents.map(doc => doc.id)));
  }, [documents]);

  const selectNone = useCallback(() => {
    setSelectedIds(new Set());
  }, []);

  const selectByType = useCallback((type: string) => {
    const typeDocuments = documents.filter(doc => doc.type === type);
    setSelectedIds(new Set(typeDocuments.map(doc => doc.id)));
  }, [documents]);

  const selectByFilter = useCallback((filterFn: (doc: Document) => boolean) => {
    const filteredDocuments = documents.filter(filterFn);
    setSelectedIds(new Set(filteredDocuments.map(doc => doc.id)));
  }, [documents]);

  const isAllSelected = useMemo(() => {
    return documents.length > 0 && selectedIds.size === documents.length;
  }, [documents.length, selectedIds.size]);

  const isNoneSelected = useMemo(() => {
    return selectedIds.size === 0;
  }, [selectedIds.size]);

  const isPartiallySelected = useMemo(() => {
    return selectedIds.size > 0 && selectedIds.size < documents.length;
  }, [selectedIds.size, documents.length]);

  const selectionStats = useMemo(() => {
    const stats = {
      total: selectedDocuments.length,
      byType: {} as Record<string, number>,
      totalSize: 0 // Could be calculated if document sizes are available
    };

    selectedDocuments.forEach(doc => {
      stats.byType[doc.type] = (stats.byType[doc.type] || 0) + 1;
    });

    return stats;
  }, [selectedDocuments]);

  return {
    selectedDocuments,
    selectedIds,
    isSelected,
    toggleSelection,
    selectAll,
    selectNone,
    selectByType,
    selectByFilter,
    isAllSelected,
    isNoneSelected,
    isPartiallySelected,
    selectionStats,
    setSelectedDocuments: (documents: Document[]) => {
      setSelectedIds(new Set(documents.map(doc => doc.id)));
    }
  };
}
