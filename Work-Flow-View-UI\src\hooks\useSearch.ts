import { useState, useCallback, useMemo } from 'react';
import { SearchFilters } from '../components/SearchBar';
import { Document } from '../types';

interface UseSearchOptions {
  documents: Document[];
  searchFields?: (keyof Document)[];
}

interface SearchResult {
  document: Document;
  score: number;
  matches: string[];
}

export function useSearch({ documents, searchFields = ['title', 'description'] }: UseSearchOptions) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    query: '',
    type: 'all',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });
  const [isSearching, setIsSearching] = useState(false);

  // Simple text search scoring
  const calculateRelevanceScore = useCallback((document: Document, query: string): number => {
    if (!query.trim()) return 0;

    const queryTerms = query.toLowerCase().split(/\s+/);
    let score = 0;

    searchFields.forEach(field => {
      const fieldValue = document[field]?.toString().toLowerCase() || '';
      
      queryTerms.forEach(term => {
        // Exact match in title gets highest score
        if (field === 'title' && fieldValue.includes(term)) {
          score += fieldValue === term ? 10 : 5;
        }
        // Partial matches in other fields
        else if (fieldValue.includes(term)) {
          score += 2;
        }
        // Fuzzy matching (simple)
        else if (fieldValue.includes(term.substring(0, Math.max(3, term.length - 1)))) {
          score += 1;
        }
      });
    });

    return score;
  }, [searchFields]);

  // Filter documents based on search criteria
  const filteredDocuments = useMemo(() => {
    let results = documents;

    // Apply type filter
    if (searchFilters.type && searchFilters.type !== 'all') {
      results = results.filter(doc => doc.type === searchFilters.type);
    }

    // Apply faculty filter
    if (searchFilters.faculty?.trim()) {
      const facultyQuery = searchFilters.faculty.toLowerCase();
      results = results.filter(doc => 
        doc.facultyName?.toLowerCase().includes(facultyQuery) ||
        doc.instructor?.toLowerCase().includes(facultyQuery)
      );
    }

    // Apply date range filter
    if (searchFilters.dateRange?.start || searchFilters.dateRange?.end) {
      results = results.filter(doc => {
        const docDate = new Date(doc.createdDate || doc.scheduledDate || doc.sessionDate || '');
        const startDate = searchFilters.dateRange?.start ? new Date(searchFilters.dateRange.start) : null;
        const endDate = searchFilters.dateRange?.end ? new Date(searchFilters.dateRange.end) : null;

        if (startDate && docDate < startDate) return false;
        if (endDate && docDate > endDate) return false;
        return true;
      });
    }

    // Apply search query
    if (searchQuery.trim()) {
      const searchResults: SearchResult[] = results
        .map(doc => ({
          document: doc,
          score: calculateRelevanceScore(doc, searchQuery),
          matches: [] // Could be enhanced to show matching text snippets
        }))
        .filter(result => result.score > 0);

      // Sort by relevance if that's the selected sort
      if (searchFilters.sortBy === 'relevance') {
        searchResults.sort((a, b) => b.score - a.score);
        return searchResults.map(result => result.document);
      }

      results = searchResults.map(result => result.document);
    }

    // Apply sorting
    results.sort((a, b) => {
      let comparison = 0;

      switch (searchFilters.sortBy) {
        case 'title':
          comparison = (a.title || '').localeCompare(b.title || '');
          break;
        case 'date':
          const dateA = new Date(a.createdDate || a.scheduledDate || a.sessionDate || '');
          const dateB = new Date(b.createdDate || b.scheduledDate || b.sessionDate || '');
          comparison = dateA.getTime() - dateB.getTime();
          break;
        case 'type':
          comparison = (a.type || '').localeCompare(b.type || '');
          break;
        case 'relevance':
        default:
          // Already handled above for search results
          break;
      }

      return searchFilters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return results;
  }, [documents, searchQuery, searchFilters, calculateRelevanceScore]);

  const handleSearch = useCallback(async (query: string, filters: SearchFilters) => {
    setIsSearching(true);
    
    // Simulate API delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setSearchQuery(query);
    setSearchFilters(filters);
    setIsSearching(false);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchFilters({
      query: '',
      type: 'all',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
  }, []);

  const hasActiveSearch = searchQuery.trim() !== '' || 
    searchFilters.type !== 'all' || 
    searchFilters.faculty?.trim() !== '' ||
    searchFilters.dateRange?.start ||
    searchFilters.dateRange?.end;

  return {
    searchQuery,
    searchFilters,
    filteredDocuments,
    isSearching,
    hasActiveSearch,
    handleSearch,
    clearSearch,
    resultCount: filteredDocuments.length
  };
}

// Hook for search history
export function useSearchHistory() {
  const [searchHistory, setSearchHistory] = useState<string[]>(() => {
    try {
      const saved = localStorage.getItem('searchHistory');
      return saved ? JSON.parse(saved) : [];
    } catch {
      return [];
    }
  });

  const addToHistory = useCallback((query: string) => {
    if (!query.trim()) return;

    setSearchHistory(prev => {
      const newHistory = [query, ...prev.filter(item => item !== query)].slice(0, 10);
      localStorage.setItem('searchHistory', JSON.stringify(newHistory));
      return newHistory;
    });
  }, []);

  const clearHistory = useCallback(() => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  }, []);

  return {
    searchHistory,
    addToHistory,
    clearHistory
  };
}
