using System.ComponentModel.DataAnnotations;

namespace WorkFlowViewMgmt.Domain.Entities.Filter
{
    public class Semester : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;

        [Required]
        public int DepartmentId { get; set; }

        public int? CourseId { get; set; }

        public int AcademicYearId { get; set; }

        public int LevelId { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        public int DurationWeeks { get; set; }



        public int TotalStudents { get; set; } = 0;

        [MaxLength(20)]
        public string Status { get; set; } = "Upcoming";

        public string? Description { get; set; }

        public bool ExamScheduled { get; set; } = false;

        // Navigation properties
        public virtual Department? Department { get; set; }
        public virtual Course? Course { get; set; }
    }
}
