import React, { useState } from 'react';
import { Settings, Palette, Layout, Bell, Download, <PERSON>, Moon, Sun, Monitor, Grid, List, Layers } from 'lucide-react';
import CustomSelect, { SelectOption } from './CustomSelect';

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  layout: 'grid' | 'list' | 'compact';
  itemsPerPage: number;
  autoRefresh: boolean;
  refreshInterval: number;
  notifications: {
    newDocuments: boolean;
    updates: boolean;
    reminders: boolean;
  };
  display: {
    showThumbnails: boolean;
    showMetadata: boolean;
    compactMode: boolean;
  };
  downloads: {
    defaultLocation: string;
    autoOpen: boolean;
    confirmBulkDownload: boolean;
  };
}

interface UserPreferencesProps {
  preferences: UserPreferences;
  onPreferencesChange: (preferences: UserPreferences) => void;
  onClose: () => void;
}

const UserPreferences: React.FC<UserPreferencesProps> = ({
  preferences,
  onPreferencesChange,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'appearance' | 'layout' | 'notifications' | 'downloads'>('appearance');

  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {
    onPreferencesChange({
      ...preferences,
      [key]: value
    });
  };

  const handleNestedPreferenceChange = (category: keyof UserPreferences, key: string, value: any) => {
    onPreferencesChange({
      ...preferences,
      [category]: {
        ...preferences[category],
        [key]: value
      }
    });
  };

  const tabs = [
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'layout', label: 'Layout', icon: Layout },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'downloads', label: 'Downloads', icon: Download }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Settings className="h-6 w-6 text-blue-600" />
            <h2 className="text-2xl font-bold text-gray-900">User Preferences</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
          >
            ✕
          </button>
        </div>

        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-gray-50 p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'appearance' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Appearance Settings</h3>
                
                {/* Theme Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Theme
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {[
                      { value: 'light', label: 'Light', icon: Sun },
                      { value: 'dark', label: 'Dark', icon: Moon },
                      { value: 'system', label: 'System', icon: Monitor }
                    ].map((theme) => {
                      const Icon = theme.icon;
                      return (
                        <button
                          key={theme.value}
                          onClick={() => handlePreferenceChange('theme', theme.value)}
                          className={`flex flex-col items-center p-4 rounded-xl border-2 transition-colors ${
                            preferences.theme === theme.value
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <Icon className="h-6 w-6 mb-2" />
                          <span className="text-sm font-medium">{theme.label}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Display Options */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Display Options
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.display.showThumbnails}
                        onChange={(e) => handleNestedPreferenceChange('display', 'showThumbnails', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show document thumbnails</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.display.showMetadata}
                        onChange={(e) => handleNestedPreferenceChange('display', 'showMetadata', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show detailed metadata</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={preferences.display.compactMode}
                        onChange={(e) => handleNestedPreferenceChange('display', 'compactMode', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">Compact mode</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'layout' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Layout Settings</h3>
                
                {/* Layout Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Document Layout
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {[
                      { value: 'grid', label: 'Grid View' },
                      { value: 'list', label: 'List View' },
                      { value: 'compact', label: 'Compact View' }
                    ].map((layout) => (
                      <button
                        key={layout.value}
                        onClick={() => handlePreferenceChange('layout', layout.value)}
                        className={`p-4 rounded-xl border-2 transition-colors ${
                          preferences.layout === layout.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <span className="text-sm font-medium">{layout.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Items Per Page */}
                <CustomSelect
                  label="Items Per Page"
                  options={[
                    { value: '10', label: '10 items', icon: <Eye className="h-4 w-4 text-blue-600" /> },
                    { value: '25', label: '25 items', icon: <Eye className="h-4 w-4 text-green-600" /> },
                    { value: '50', label: '50 items', icon: <Eye className="h-4 w-4 text-orange-600" /> },
                    { value: '100', label: '100 items', icon: <Eye className="h-4 w-4 text-red-600" /> }
                  ]}
                  value={preferences.itemsPerPage.toString()}
                  onChange={(value) => handlePreferenceChange('itemsPerPage', parseInt(value))}
                  size="sm"
                  variant="outlined"
                />

                {/* Auto Refresh */}
                <div>
                  <label className="flex items-center mb-3">
                    <input
                      type="checkbox"
                      checked={preferences.autoRefresh}
                      onChange={(e) => handlePreferenceChange('autoRefresh', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm font-medium text-gray-700">Auto-refresh content</span>
                  </label>
                  
                  {preferences.autoRefresh && (
                    <CustomSelect
                      label="Refresh Interval (minutes)"
                      options={[
                        { value: '1', label: '1 minute', icon: <Bell className="h-4 w-4 text-red-600" /> },
                        { value: '5', label: '5 minutes', icon: <Bell className="h-4 w-4 text-orange-600" /> },
                        { value: '10', label: '10 minutes', icon: <Bell className="h-4 w-4 text-green-600" /> },
                        { value: '30', label: '30 minutes', icon: <Bell className="h-4 w-4 text-blue-600" /> }
                      ]}
                      value={preferences.refreshInterval.toString()}
                      onChange={(value) => handlePreferenceChange('refreshInterval', parseInt(value))}
                      size="sm"
                      variant="outlined"
                    />
                  )}
                </div>
              </div>
            )}

            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
                
                <div className="space-y-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.newDocuments}
                      onChange={(e) => handleNestedPreferenceChange('notifications', 'newDocuments', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Notify about new documents</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.updates}
                      onChange={(e) => handleNestedPreferenceChange('notifications', 'updates', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Notify about document updates</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={preferences.notifications.reminders}
                      onChange={(e) => handleNestedPreferenceChange('notifications', 'reminders', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Show reminders</span>
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'downloads' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Download Settings</h3>
                
                <div className="space-y-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={preferences.downloads.autoOpen}
                      onChange={(e) => handleNestedPreferenceChange('downloads', 'autoOpen', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Auto-open downloaded files</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={preferences.downloads.confirmBulkDownload}
                      onChange={(e) => handleNestedPreferenceChange('downloads', 'confirmBulkDownload', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Confirm bulk downloads</span>
                  </label>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserPreferences;
