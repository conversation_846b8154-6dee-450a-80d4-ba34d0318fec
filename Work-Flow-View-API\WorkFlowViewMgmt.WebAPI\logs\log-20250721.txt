2025-07-21 00:00:01.152 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4067.2017ms
2025-07-21 00:00:01.154 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.154 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:01.160 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - 500 null application/json 4075.3613ms
2025-07-21 00:00:01.163 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - null null
2025-07-21 00:00:01.163 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:01.163 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.163 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:01.168 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4068.0268ms
2025-07-21 00:00:01.168 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4066.1978ms
2025-07-21 00:00:01.168 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4070.2386ms
2025-07-21 00:00:01.168 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.168 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.168 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.168 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:01.168 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:01.168 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:01.173 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - 500 null application/json 4074.4573ms
2025-07-21 00:00:01.175 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - 500 null application/json 4074.1664ms
2025-07-21 00:00:01.175 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - null null
2025-07-21 00:00:01.179 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - null null
2025-07-21 00:00:01.179 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - 500 null application/json 4082.5454ms
2025-07-21 00:00:01.180 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:01.180 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:01.180 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.180 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.180 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:01.180 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:01.182 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - null null
2025-07-21 00:00:01.182 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:01.182 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.182 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:01.184 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - null null
2025-07-21 00:00:01.184 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:01.184 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:01.184 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:05.224 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4060.5979ms
2025-07-21 00:00:05.224 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.224 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:05.229 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - 500 null application/json 4066.0724ms
2025-07-21 00:00:05.240 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4057.4402ms
2025-07-21 00:00:05.240 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.241 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4060.8109ms
2025-07-21 00:00:05.241 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4060.8465ms
2025-07-21 00:00:05.241 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.241 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.240 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:05.241 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:05.241 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:05.244 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - 500 null application/json 4062.6638ms
2025-07-21 00:00:05.246 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - 500 null application/json 4066.9447ms
2025-07-21 00:00:05.249 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - 500 null application/json 4073.8751ms
2025-07-21 00:00:05.249 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - null null
2025-07-21 00:00:05.249 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:05.249 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.249 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:05.252 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - null null
2025-07-21 00:00:05.252 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:05.252 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.252 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:05.272 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4087.1758ms
2025-07-21 00:00:05.272 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.272 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:05.276 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - 500 null application/json 4092.3958ms
2025-07-21 00:00:05.277 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - null null
2025-07-21 00:00:05.277 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:05.278 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.278 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:05.280 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - null null
2025-07-21 00:00:05.280 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:05.280 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:05.280 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:09.322 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4072.2752ms
2025-07-21 00:00:09.322 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:09.322 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:09.328 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - 500 null application/json 4078.9517ms
2025-07-21 00:00:09.342 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4064.5703ms
2025-07-21 00:00:09.342 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4089.7897ms
2025-07-21 00:00:09.342 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4062.3536ms
2025-07-21 00:00:09.342 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:09.342 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:09.342 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:09.342 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:09.342 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:09.342 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:09.348 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - 500 null application/json 4070.6479ms
2025-07-21 00:00:09.350 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - null null
2025-07-21 00:00:09.353 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - 500 null application/json 4100.6068ms
2025-07-21 00:00:09.353 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:09.353 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:09.350 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - 500 null application/json 4070.4969ms
2025-07-21 00:00:09.353 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:09.353 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - null null
2025-07-21 00:00:09.354 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:09.354 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:09.354 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:09.363 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - null null
2025-07-21 00:00:09.364 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:09.364 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:09.364 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:13.399 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4045.6899ms
2025-07-21 00:00:13.399 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:13.399 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4044.263ms
2025-07-21 00:00:13.399 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:13.399 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:13.399 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:13.403 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - 500 null application/json 4053.2251ms
2025-07-21 00:00:13.406 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - 500 null application/json 4053.5115ms
2025-07-21 00:00:13.408 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - null null
2025-07-21 00:00:13.408 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:13.408 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:13.408 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:13.413 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4049.582ms
2025-07-21 00:00:13.414 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:13.414 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:13.419 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - 500 null application/json 4055.8903ms
2025-07-21 00:00:13.423 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - null null
2025-07-21 00:00:13.424 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:13.424 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:13.424 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:14.257 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - null null
2025-07-21 00:00:14.257 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:14.258 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:14.258 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:17.478 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4053.9445ms
2025-07-21 00:00:17.478 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4069.3731ms
2025-07-21 00:00:17.478 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:17.478 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:17.478 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:17.478 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:17.485 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - 500 null application/json 4077.5838ms
2025-07-21 00:00:17.485 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - 500 null application/json 4061.8741ms
2025-07-21 00:00:17.491 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - null null
2025-07-21 00:00:17.492 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:17.492 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:17.492 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:18.321 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4063.7499ms
2025-07-21 00:00:18.322 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:18.322 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:18.327 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c99e501b-e7f9-46ac-9dae-f11237a295c0/stats - 500 null application/json 4069.4229ms
2025-07-21 00:00:18.331 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - null null
2025-07-21 00:00:18.331 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:18.332 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:18.332 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:21.589 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4096.737ms
2025-07-21 00:00:21.589 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:21.589 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:21.594 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - 500 null application/json 4102.9304ms
2025-07-21 00:00:22.423 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4091.4925ms
2025-07-21 00:00:22.424 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:22.424 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:22.427 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/8cbfbd6b-f978-4372-a85e-7b3b0494b79b/stats - 500 null application/json 4096.1516ms
2025-07-21 00:00:22.431 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - null null
2025-07-21 00:00:22.431 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:22.431 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:22.431 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:26.494 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4062.6577ms
2025-07-21 00:00:26.494 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:26.494 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:26.501 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/d64afe0b-62e1-441d-8e8e-0f9809911b19/stats - 500 null application/json 4069.3574ms
2025-07-21 00:00:26.506 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - null null
2025-07-21 00:00:26.506 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:26.506 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:26.506 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:30.589 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4083.3376ms
2025-07-21 00:00:30.589 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:30.590 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:30.593 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/a4a815d1-44d2-4310-aa6e-08f3b5c73a7b/stats - 500 null application/json 4087.0126ms
2025-07-21 00:00:30.596 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - null null
2025-07-21 00:00:30.596 +05:30 [INF] CORS policy execution successful.
2025-07-21 00:00:30.596 +05:30 [INF] Executing endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:30.596 +05:30 [INF] Route matched with {action = "GetFeedbackStatsByLessonId", controller = "Feedback"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeedbackStatsByLessonId(System.Guid) on controller WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController (WorkFlowViewMgmt.WebAPI).
2025-07-21 00:00:34.641 +05:30 [INF] Executed action WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI) in 4044.9419ms
2025-07-21 00:00:34.641 +05:30 [INF] Executed endpoint 'WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId (WorkFlowViewMgmt.WebAPI)'
2025-07-21 00:00:34.641 +05:30 [ERR] An unhandled exception occurred
Npgsql.NpgsqlException (0x80004005): Failed to connect to 127.0.0.1:5432
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.Connect(NpgsqlTimeout timeout)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|214_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at WorkFlowViewMgmt.Infrastructure.UnitOfWork.UnitOfWork..ctor(IDbConnectionFactory connectionFactory) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Infrastructure\UnitOfWork\UnitOfWork.cs:line 23
   at InvokeStub_UnitOfWork..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at MediatR.Wrappers.RequestHandlerWrapperImpl`2.<>c__DisplayClass1_0.<Handle>g__Handler|0(CancellationToken t)
   at WorkFlowViewMgmt.Application.Common.Behaviours.ValidationBehavior`2.Handle(TRequest request, RequestHandlerDelegate`1 next, CancellationToken cancellationToken) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.Application\Common\Behaviours\ValidationBehavior.cs:line 34
   at WorkFlowViewMgmt.WebAPI.Controllers.FeedbackController.GetFeedbackStatsByLessonId(Guid lessonId) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Controllers\FeedbackController.cs:line 36
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WorkFlowViewMgmt.WebAPI.Middlewares.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in E:\muneesh\Workflow\Work-Flow-View-API\WorkFlowViewMgmt.WebAPI\Middlewares\ExceptionHandlingMiddleware.cs:line 22
2025-07-21 00:00:34.647 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/feedback/lesson/c2fabae2-efe8-42e4-8881-526a1da1ba1c/stats - 500 null application/json 4050.6264ms
