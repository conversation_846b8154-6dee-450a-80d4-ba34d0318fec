using Dapper;
using System.Data;
using WorkFlowViewMgmt.Domain.Entities.TreeView;
using WorkFlowViewMgmt.Domain.IRepository;
using WorkFlowViewMgmt.Infrastructure.RepositoryBase;

namespace WorkFlowViewMgmt.Infrastructure.Repository
{
    public class TreeViewRepository : RepositoryTranBase, ITreeViewRepository
    {
        public TreeViewRepository(IDbTransaction transaction) : base(transaction)
        {
        }

        public async Task<IEnumerable<TreeViewItem>> GetTreeViewDataAsync(
            int? degreeId = null,
            int? departmentId = null,
            string? academicYear = null,
            int? semesterId = null,
            int? courseId = null)
        {
            // If only department is selected, return only syllabi (no lesson plans or sessions)
            if (departmentId.HasValue && !degreeId.HasValue && string.IsNullOrEmpty(academicYear) && !semesterId.HasValue && !courseId.HasValue)
            {
                var syllabusOnlyQuery = @"
                    SELECT
                        s.id,
                        s.title,
                        'Syllabus' as type,
                        s.status,
                        s.document_url as url,
                        s.created_date as createddate,
                        s.modified_date as modifieddate,
                        s.faculty_name as facultyname,
                        s.is_active as isactive,
                        s.credits,
                        s.duration_weeks as durationweeks,
                        s.department_id as departmentid,
                        s.course_id as courseid,
                        s.semester_id as semesterid
                    FROM workflowmgmt.syllabi s
                    WHERE s.is_active = true AND s.department_id = @DepartmentId
                    ORDER BY s.title";

                var syllabusParameters = new { DepartmentId = departmentId.Value };
                return await Connection.QueryAsync<TreeViewItem>(syllabusOnlyQuery, syllabusParameters, transaction: Transaction);
            }

            var parameters = new DynamicParameters();
            var whereConditions = new List<string> { "s.is_active = true" };

            // Build WHERE conditions based on filters
            if (departmentId.HasValue)
            {
                whereConditions.Add("s.department_id = @DepartmentId");
                parameters.Add("DepartmentId", departmentId.Value);
            }

            if (courseId.HasValue)
            {
                whereConditions.Add("s.course_id = @CourseId");
                parameters.Add("CourseId", courseId.Value);
            }

            if (semesterId.HasValue)
            {
                whereConditions.Add("s.semester_id = @SemesterId");
                parameters.Add("SemesterId", semesterId.Value);
            }

            // For academic year, we need to join with semesters table if not already filtered by semester
            if (!string.IsNullOrEmpty(academicYear) && !semesterId.HasValue)
            {
                whereConditions.Add("EXISTS (SELECT 1 FROM workflowmgmt.semesters sem WHERE sem.id = s.semester_id AND sem.academic_year_id = @AcademicYear::int AND sem.is_active = true)");
                parameters.Add("AcademicYear", academicYear);
            }

            var whereClause = string.Join(" AND ", whereConditions);

            // Get all syllabi with filters
            var syllabusQuery = $@"
                SELECT
                    s.id,
                    s.title,
                    'Syllabus' as type,
                    s.status,
                    s.document_url as url,
                    s.created_date as createddate,
                    s.modified_date as modifieddate,
                    s.faculty_name as facultyname,
                    s.is_active as isactive,
                    s.credits,
                    s.duration_weeks as durationweeks,
                    s.department_id as departmentid,
                    s.course_id as courseid,
                    s.semester_id as semesterid
                FROM workflowmgmt.syllabi s
                WHERE {whereClause}
                ORDER BY s.title";

            var syllabi = await Connection.QueryAsync<TreeViewItem>(syllabusQuery, parameters, transaction: Transaction);

            var result = new List<TreeViewItem>();

            foreach (var syllabus in syllabi)
            {
                // Get lesson plans for this syllabus
                var lessonPlanQuery = @"
                    SELECT
                        lp.id,
                        lp.title,
                        'LessonPlan' as type,
                        lp.status,
                        lp.document_url as url,
                        lp.created_date as createddate,
                        lp.modified_date as modifieddate,
                        lp.faculty_name as facultyname,
                        lp.is_active as isactive,
                        lp.duration_minutes as durationminutes,
                        lp.number_of_sessions as numberofsessions,
                        lp.scheduled_date as scheduleddate
                    FROM workflowmgmt.lesson_plans lp
                    WHERE lp.syllabus_id = @SyllabusId AND lp.is_active = true
                    ORDER BY lp.title";

                var lessonPlans = await Connection.QueryAsync<TreeViewItem>(
                    lessonPlanQuery, 
                    new { SyllabusId = syllabus.Id }, 
                    transaction: Transaction);

                foreach (var lessonPlan in lessonPlans)
                {
                    // Get sessions for this lesson plan
                    var sessionQuery = @"
                        SELECT
                            sess.id,
                            sess.title,
                            'Session' as type,
                            sess.status,
                            sess.document_url as url,
                            sess.created_date as createddate,
                            sess.modified_date as modifieddate,
                            sess.instructor,
                            sess.is_active as isactive,
                            sess.duration_minutes as durationminutes,
                            sess.session_date as sessiondate,
                            sess.session_time as sessiontime,
                            sess.teaching_method as teachingmethod
                        FROM workflowmgmt.sessions sess
                        WHERE sess.lesson_plan_id = @LessonPlanId AND sess.is_active = true
                        ORDER BY sess.session_date, sess.session_time, sess.title";

                    var sessions = await Connection.QueryAsync<TreeViewItem>(
                        sessionQuery, 
                        new { LessonPlanId = lessonPlan.Id }, 
                        transaction: Transaction);

                    lessonPlan.Children = sessions.ToList();
                }

                syllabus.Children = lessonPlans.ToList();
                result.Add(syllabus);
            }

            return result;
        }
    }
}
