import React, { useEffect, useState } from 'react';
import { X, FileText } from 'lucide-react';
import { Document } from '../types';
import DocumentFeedback from './DocumentFeedback';
import { apiConfig } from '../services/config';

interface DocumentViewerProps {
  document: Document;
  onClose: () => void;
  onDownload: (document: Document) => void;
  courseInfo?: any;
  user?: any;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  document,
  onClose,
  onDownload,
  courseInfo,
  user,
}) => {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(true);

  // Reset loading state when document changes
  useEffect(() => {
    setPdfLoading(true);
  }, [document.url]);

  // Add keyboard support for ESC key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  const handlePdfLoad = () => {
    setPdfLoading(false);
    console.log('PDF loaded successfully');
  };

  const handlePdfError = (e: any) => {
    setPdfLoading(false);
    console.error('Error loading PDF:', e);
    // Try alternative viewer with Google Docs
    const iframe = e.target as HTMLIFrameElement;
    if (iframe && document.url) {
      // If it's a relative URL, make it absolute
      const fullUrl = document.url.startsWith('http')
        ? document.url
        : `http://localhost:5000${document.url}`;

      iframe.src = `https://docs.google.com/viewer?url=${encodeURIComponent(fullUrl)}&embedded=true`;
    }
  };

  // Get the full URL for the document
  const getDocumentUrl = () => {
    if (!document.url) return '';
    return apiConfig.getDocumentUrl(document.url);
  };

  const closeFeedbackModal = () => {
    setShowFeedbackModal(false);
  };



  // Clean document viewer - removed all complex HTML generation and new tab functionality

  return (
    <div className="fixed inset-0 bg-white z-50">
      {/* Minimal close button - small and unobtrusive */}
      <button
        onClick={onClose}
        className="absolute top-2 right-2 z-10 p-1 bg-gray-200 hover:bg-gray-300 text-gray-600 rounded-full transition-all duration-200 opacity-70 hover:opacity-100"
        title="Close (ESC)"
      >
        <X className="h-4 w-4" />
      </button>

      {/* Clean Document Viewer - Full Screen */}
      <div className="w-full h-full">
        {document.url ? (
          <div className="w-full h-full relative">
            {/* Clean PDF Viewer without any UI elements */}
            <iframe
              src={`${getDocumentUrl()}#toolbar=0&navpanes=0&scrollbar=1&page=1&view=FitH&zoom=page-width`}
              className="w-full h-full border-0"
              title={document.title}
              allow="fullscreen"
              sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
              onLoad={handlePdfLoad}
              onError={handlePdfError}
            />

            {/* Minimal loading indicator */}
            {pdfLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
                <div className="text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
                  <p className="text-gray-500 text-sm">Loading document...</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full bg-white">
            <div className="text-center">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <FileText className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-base font-medium text-gray-700 mb-1">Document Not Available</h3>
              <p className="text-gray-500 text-sm">The document URL is not available for viewing.</p>
            </div>
          </div>
        )}
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && (
        <DocumentFeedback
          documentId={document.id}
          documentTitle={document.title}
          documentType={document.type === 'Syllabus' ? 'syllabus' : 'lesson'}
          onClose={closeFeedbackModal}
          user={user}
        />
      )}
    </div>
  );
};

export default DocumentViewer;