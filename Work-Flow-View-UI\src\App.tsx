import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import FilterPanel from './components/FilterPanel';
import HierarchicalDocumentList from './components/HierarchicalDocumentList';
import DocumentViewer from './components/DocumentViewer';
import FeedbackTest from './components/FeedbackTest';
import { Document } from './types';
import { useTreeViewData } from './hooks/useTreeViewData';
import { TreeViewItem } from './services/treeViewService';
import { apiConfig } from './services/config';

function App() {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [currentFilters, setCurrentFilters] = useState<any>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [user, setUser] = useState<any>(null);
  const { treeData, loading: treeLoading, error: treeError, loadTreeData } = useTreeViewData();

  // Check for existing login on component mount
  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
  }, []);

  const handleViewDocument = (document: Document) => {
    // Open document in new tab instead of modal
    if (document.url) {
      openDocumentInNewTab(document);
    } else {
      // Fallback to modal if no URL
      setSelectedDocument(document);
    }
  };

  const openDocumentInNewTab = (document: Document) => {
    console.log('Opening document:', document);

    // Create URL with both approaches for maximum compatibility
    const documentData = {
      id: document.id,
      title: document.title,
      type: document.type,
      status: document.status,
      url: document.url,
      facultyName: document.facultyName,
      createdDate: document.createdDate,
      credits: document.credits,
      durationMinutes: document.durationMinutes,
      durationWeeks: document.durationWeeks,
      numberOfSessions: document.numberOfSessions,
      scheduledDate: document.scheduledDate,
      sessionDate: document.sessionDate,
      sessionTime: document.sessionTime,
      teachingMethod: document.teachingMethod,
      instructor: document.instructor,
      modifiedDate: document.modifiedDate
    };

    // Approach 1: Direct URL parameters (simpler, more reliable)
    const params = new URLSearchParams({
      id: document.id,
      title: document.title,
      type: document.type,
      url: document.url || '',
      ...(document.facultyName && { faculty: document.facultyName }),
      ...(document.createdDate && { created: document.createdDate })
    });

    // Approach 2: Encoded data object (fallback)
    const encodedData = encodeURIComponent(JSON.stringify(documentData));
    params.append('data', encodedData);

    const viewerUrl = `${window.location.origin}/document-viewer?${params.toString()}`;
    console.log('Opening viewer URL:', viewerUrl);

    // Open in new tab
    window.open(viewerUrl, '_blank');
  };

  const generateDocumentViewerHTML = (doc: Document, documentUrl: string) => {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${doc.title} - Document Viewer</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
          }
          .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          }
          .title-section {
            display: flex;
            align-items: center;
            gap: 1rem;
          }
          .doc-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
          }
          .title-info h1 {
            font-size: 1.5rem;
            color: #1a202c;
            margin-bottom: 0.25rem;
          }
          .doc-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            ${getDocumentTypeStyleForHTML(doc.type)}
          }
          .doc-info {
            display: flex;
            gap: 2rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #4a5568;
          }
          .actions {
            display: flex;
            gap: 1rem;
          }
          .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
          }
          .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
          }
          .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
          }
          .btn-secondary {
            background: #f7fafc;
            color: #2d3748;
            border: 1px solid #e2e8f0;
          }
          .btn-secondary:hover {
            background: #edf2f7;
          }
          .document-container {
            flex: 1;
            padding: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .document-frame {
            width: 100%;
            max-width: 1200px;
            height: 80vh;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
          }
          iframe, embed, object {
            width: 100%;
            height: 100%;
            border: none;
          }
          .error-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            text-align: center;
            padding: 2rem;
          }
          .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title-section">
            <div class="doc-icon">${getDocumentIcon(doc.type)}</div>
            <div class="title-info">
              <h1>${doc.title}</h1>
              <div style="display: flex; align-items: center; gap: 1rem;">
                <span class="doc-type">${doc.type}</span>
                <span style="color: #4a5568; font-size: 0.875rem;">Status: ${doc.status}</span>
              </div>
              <div class="doc-info">
                ${doc.facultyName ? `<span>👨‍🏫 ${doc.facultyName}</span>` : ''}
                ${doc.createdDate ? `<span>📅 ${new Date(doc.createdDate).toLocaleDateString()}</span>` : ''}
                ${doc.type === 'Syllabus' && doc.credits ? `<span>🎓 ${doc.credits} Credits</span>` : ''}
                ${doc.durationMinutes ? `<span>⏱️ ${doc.durationMinutes} min</span>` : ''}
              </div>
            </div>
          </div>
          <div class="actions">
            <a href="${documentUrl}" download class="btn btn-primary">
              📥 Download
            </a>
            <button onclick="window.close()" class="btn btn-secondary">
              ✕ Close
            </button>
          </div>
        </div>
        <div class="document-container">
          <div class="document-frame">
            ${generateDocumentEmbed(documentUrl)}
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const getDocumentTypeStyleForHTML = (type: string) => {
    switch (type) {
      case 'Syllabus':
        return 'background: linear-gradient(135deg, #dbeafe, #bfdbfe); color: #1e40af; border: 1px solid #93c5fd;';
      case 'Lesson':
        return 'background: linear-gradient(135deg, #d1fae5, #a7f3d0); color: #065f46; border: 1px solid #6ee7b7;';
      case 'Session':
        return 'background: linear-gradient(135deg, #e9d5ff, #ddd6fe); color: #581c87; border: 1px solid #c4b5fd;';
      default:
        return 'background: linear-gradient(135deg, #f1f5f9, #e2e8f0); color: #334155; border: 1px solid #cbd5e1;';
    }
  };

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'Syllabus': return '📚';
      case 'Lesson': return '📝';
      case 'Session': return '🎯';
      default: return '📄';
    }
  };

  const generateDocumentEmbed = (documentUrl: string) => {
    const fileExtension = documentUrl.split('.').pop()?.toLowerCase();

    if (fileExtension === 'pdf') {
      return `<iframe src="${documentUrl}" type="application/pdf"></iframe>`;
    } else if (['doc', 'docx'].includes(fileExtension || '')) {
      // For Word documents, try to use Google Docs viewer
      return `<iframe src="https://docs.google.com/viewer?url=${encodeURIComponent(documentUrl)}&embedded=true"></iframe>`;
    } else {
      return `
        <div class="error-message">
          <div class="error-icon">📄</div>
          <h3>Document Preview Not Available</h3>
          <p>This document type cannot be previewed in the browser.</p>
          <p>Please use the download button to view the document.</p>
          <a href="${documentUrl}" download class="btn btn-primary" style="margin-top: 1rem;">
            📥 Download Document
          </a>
        </div>
      `;
    }
  };

  const handleDownloadDocument = (document: Document) => {
    if (document.url) {
      const documentUrl = apiConfig.getDocumentUrl(document.url);

      // Create a temporary link element and trigger download
      const link = document.createElement('a');
      link.href = documentUrl;
      link.download = document.title || 'document';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      alert(`Document URL not available for: ${document.title}`);
    }
  };

  const handleCloseViewer = () => {
    setSelectedDocument(null);
  };

  // Load tree data only when degree and department are selected
  useEffect(() => {
    if (currentFilters && currentFilters.degree && currentFilters.department) {
      loadTreeData({
        degreeId: currentFilters.degree ? Number(currentFilters.degree) : undefined,
        departmentId: currentFilters.department ? Number(currentFilters.department) : undefined,
        academicYear: currentFilters.academicYear || undefined,
        semesterId: currentFilters.semester ? Number(currentFilters.semester) : undefined,
        courseId: currentFilters.course ? Number(currentFilters.course) : undefined
      });
    } else {
      // Clear tree data if degree or department is not selected
      // This will be handled by the hook internally
    }
  }, [currentFilters, loadTreeData]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  // Convert TreeViewItem to Document format
  const convertTreeItemToDocument = (item: TreeViewItem): Document => {
    // Map LessonPlan to Lesson for Document interface compatibility
    const documentType = item.type === 'LessonPlan' ? 'Lesson' : item.type as 'Syllabus' | 'Lesson' | 'Session';

    return {
      id: item.id,
      title: item.title,
      type: documentType,
      status: item.status,
      url: item.url || '',
      createdDate: item.createdDate || '',
      modifiedDate: item.modifiedDate || '',
      facultyName: item.facultyName || '',
      isActive: item.isActive,
      // Map additional properties based on type
      credits: item.credits,
      durationWeeks: item.durationWeeks,
      durationMinutes: item.durationMinutes,
      numberOfSessions: item.numberOfSessions,
      scheduledDate: item.scheduledDate,
      sessionDate: item.sessionDate,
      sessionTime: item.sessionTime,
      teachingMethod: item.teachingMethod,
      instructor: item.instructor,
      // For compatibility with existing Document interface
      courseId: item.courseId,
      semesterId: item.semesterId,
      academicYearId: item.academicYear ? parseInt(item.academicYear) : undefined,
      parentId: undefined // Will be set during hierarchy building
    };
  };

  const buildHierarchy = (treeItems: TreeViewItem[]) => {
    const hierarchy = treeItems.map(syllabus => {
      const syllabusDoc = convertTreeItemToDocument(syllabus);

      const lessonsWithSessions = syllabus.children.map(lessonPlan => {
        const lessonDoc = convertTreeItemToDocument(lessonPlan);
        lessonDoc.parentId = syllabus.id;

        const sessions = lessonPlan.children.map(session => {
          const sessionDoc = convertTreeItemToDocument(session);
          sessionDoc.parentId = lessonPlan.id;

          return {
            id: session.id,
            title: session.title,
            type: session.type,
            document: sessionDoc,
            expanded: expandedItems.has(session.id)
          };
        });

        return {
          id: lessonPlan.id,
          title: lessonPlan.title,
          type: lessonPlan.type,
          document: lessonDoc,
          children: sessions,
          expanded: expandedItems.has(lessonPlan.id)
        };
      });

      return {
        id: syllabus.id,
        title: syllabus.title,
        type: syllabus.type,
        document: syllabusDoc,
        children: lessonsWithSessions,
        expanded: expandedItems.has(syllabus.id)
      };
    });

    return hierarchy;
  };

  // Get hierarchical documents from tree data
  const getHierarchicalDocuments = () => {
    return buildHierarchy(treeData);
  };

  const hierarchicalDocuments = getHierarchicalDocuments();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <Header user={user} setUser={setUser} />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16">

        {/* Document Explorer Section */}
        <div className="space-y-6 sm:space-y-8 lg:space-y-10">
          {/* <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 mb-3 sm:mb-4">
              Document Explorer
            </h2>
            <p className="text-base sm:text-lg lg:text-xl text-slate-600 max-w-3xl mx-auto px-4">
              Navigate through our comprehensive academic resource library with intelligent filtering and hierarchical organization
            </p>
          </div> */}
          
          <FilterPanel
            onFiltersChange={setCurrentFilters}
          />

          {treeError && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <p className="text-red-700 text-sm">Error loading documents: {treeError}</p>
            </div>
          )}



          {treeLoading && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
              <p className="text-blue-700 text-sm">Loading documents...</p>
            </div>
          )}

          {/* Show documents only when degree and department are selected */}
          {currentFilters && currentFilters.degree && currentFilters.department && (
            <>
              {!treeLoading && hierarchicalDocuments.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-gray-600">No documents found for the selected filters. Please try different filter options.</p>
                </div>
              )}

              {hierarchicalDocuments.length > 0 && (
                <HierarchicalDocumentList
                  hierarchicalDocuments={hierarchicalDocuments}
                  onViewDocument={handleViewDocument}
                  onDownloadDocument={handleDownloadDocument}
                  onToggleExpanded={toggleExpanded}
                  expandedItems={expandedItems}
                  selectedCourse={null}
                  selectedSemester={null}
                  selectedAcademicYear={null}
                  user={user}
                />
              )}
            </>
          )}
        </div>

        {selectedDocument && (
          <DocumentViewer
            document={selectedDocument}
            onClose={handleCloseViewer}
            onDownload={handleDownloadDocument}
            courseInfo={null}
            user={user}
          />
        )}

        {/* Feedback Test Component - Only show in development */}
        {import.meta.env.DEV && <FeedbackTest />}
      </main>
    </div>
  );
}

export default App;