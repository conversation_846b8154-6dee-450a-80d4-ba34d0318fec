using Dapper;
using System.Data;
using WorkFlowViewMgmt.Domain.Entities.Degree;
using WorkFlowViewMgmt.Domain.Entities.Filter;
using WorkFlowViewMgmt.Domain.IRepository;
using WorkFlowViewMgmt.Infrastructure.RepositoryBase;

namespace WorkFlowViewMgmt.Infrastructure.Repository
{
    public class FilterRepository : RepositoryTranBase, IFilterRepository
    {
        public FilterRepository(IDbTransaction transaction) : base(transaction)
        {
        }

        public async Task<IEnumerable<Degree>> GetActiveDegreesAsync()
        {
            const string sql = @"
                SELECT id, name, is_active as Active, created_date as CreatedDate,
                       modified_date as ModifiedDate, created_by as CreatedBy, modified_by as ModifiedBy
                FROM workflowmgmt.levels
                WHERE is_active = true
                ORDER BY sort_order, name";

            return await Connection.QueryAsync<Degree>(sql, transaction: Transaction);
        }

        public async Task<IEnumerable<Department>> GetActiveDepartmentsAsync()
        {
            const string sql = @"
                SELECT d.id, d.name, d.code, d.description, d.head_of_department as HeadOfDepartment,
                       d.email, d.phone, d.established_year as EstablishedYear, d.programs_offered as ProgramsOffered,
                       d.accreditation, d.status, d.is_active as IsActive, d.created_date as CreatedDate,
                       d.modified_date as ModifiedDate, d.created_by as CreatedBy, d.modified_by as ModifiedBy,
                       d.email_notify as EmailNotify, d.sms_notify as SmsNotify, d.in_app_notify as InAppNotify,
                       d.digest_frequency as DigestFrequency, d.level_id as LevelId
                FROM workflowmgmt.departments d
                WHERE d.is_active = true
                ORDER BY d.name";

            return await Connection.QueryAsync<Department>(sql, transaction: Transaction);
        }

        public async Task<IEnumerable<Department>> GetDepartmentsByDegreeAsync(int degreeId)
        {
            const string sql = @"
                SELECT d.id, d.name, d.code, d.description, d.head_of_department as HeadOfDepartment,
                       d.email, d.phone, d.established_year as EstablishedYear, d.programs_offered as ProgramsOffered,
                       d.accreditation, d.status, d.is_active as IsActive, d.created_date as CreatedDate,
                       d.modified_date as ModifiedDate, d.created_by as CreatedBy, d.modified_by as ModifiedBy,
                       d.email_notify as EmailNotify, d.sms_notify as SmsNotify, d.in_app_notify as InAppNotify,
                       d.digest_frequency as DigestFrequency, d.level_id as LevelId
                FROM workflowmgmt.departments d
                WHERE d.is_active = true AND d.level_id = @DegreeId
                ORDER BY id";

            return await Connection.QueryAsync<Department>(sql, new { DegreeId = degreeId }, transaction: Transaction);
        }

        public async Task<IEnumerable<AcademicYear>> GetActiveAcademicYearsAsync()
        {
            const string sql = @"
                SELECT id, name, code, start_year as StartYear, end_year as EndYear,
                       level_id as LevelId, status, description, is_active as IsActive,
                       created_date as CreatedDate, modified_date as ModifiedDate,
                       created_by as CreatedBy, modified_by as ModifiedBy
                FROM workflowmgmt.academic_years
                WHERE is_active = true
                ORDER BY start_year DESC, name";

            return await Connection.QueryAsync<AcademicYear>(sql, transaction: Transaction);
        }

        public async Task<IEnumerable<AcademicYear>> GetAcademicYearsByDegreeAsync(int degreeId)
        {
            const string sql = @"
                SELECT id, name, code, start_year as StartYear, end_year as EndYear,
                       level_id as LevelId, status, description, is_active as IsActive,
                       created_date as CreatedDate, modified_date as ModifiedDate,
                       created_by as CreatedBy, modified_by as ModifiedBy
                FROM workflowmgmt.academic_years
                WHERE is_active = true AND level_id = @DegreeId
                ORDER BY id";

            return await Connection.QueryAsync<AcademicYear>(sql, new { DegreeId = degreeId }, transaction: Transaction);
        }

        public async Task<IEnumerable<AcademicYear>> GetAcademicYearsByDegreeAndDepartmentAsync(int degreeId, int departmentId)
        {
            const string sql = @"
                SELECT ay.id, ay.name, ay.code, ay.start_year as StartYear, ay.end_year as EndYear,
                       ay.level_id as LevelId, ay.status, ay.description, ay.is_active as IsActive,
                       ay.created_date as CreatedDate, ay.modified_date as ModifiedDate,
                       ay.created_by as CreatedBy, ay.modified_by as ModifiedBy
                FROM workflowmgmt.academic_years ay
                WHERE ay.is_active = true AND ay.level_id = @DegreeId
                ORDER BY ay.start_year DESC, ay.name";

            return await Connection.QueryAsync<AcademicYear>(sql, new { DegreeId = degreeId, DepartmentId = departmentId }, transaction: Transaction);
        }

        public async Task<IEnumerable<Semester>> GetSemestersByFiltersAsync(int? degreeId, int? departmentId, int? academicYearId)
        {
            var sql = @"
                SELECT s.id, s.name, s.code, s.department_id as DepartmentId,
                       0 as CourseId, s.start_date as StartDate, s.end_date as EndDate,
                       s.duration_weeks as DurationWeeks, s.total_students as TotalStudents,
                       s.status, s.description, s.exam_scheduled as ExamScheduled, s.is_active as IsActive,
                       s.created_date as CreatedDate, s.modified_date as ModifiedDate,
                       s.created_by as CreatedBy, s.modified_by as ModifiedBy,
                       s.academic_year_id as AcademicYearId, s.level_id as LevelId
                FROM workflowmgmt.semesters s
                WHERE s.is_active = true";

            var parameters = new DynamicParameters();

            if (degreeId.HasValue)
            {
                sql += " AND s.level_id = @DegreeId";
                parameters.Add("DegreeId", degreeId.Value);
            }

            if (departmentId.HasValue)
            {
                sql += " AND s.department_id = @DepartmentId";
                parameters.Add("DepartmentId", departmentId.Value);
            }

            if (academicYearId.HasValue)
            {
                sql += " AND s.academic_year_id = @AcademicYearId";
                parameters.Add("AcademicYearId", academicYearId.Value);
            }



            sql += " ORDER BY s.name";

            return await Connection.QueryAsync<Semester>(sql, parameters, transaction: Transaction);
        }

        public async Task<IEnumerable<Course>> GetCoursesByFiltersAsync(int? degreeId, int? departmentId, int? academicYearId, int? semesterId)
        {
            var sql = @"
                SELECT DISTINCT c.id, c.name, c.code, c.description, c.credits,
                       c.course_type as CourseType, c.duration_weeks as DurationWeeks,
                       c.max_capacity as MaxCapacity, c.status, c.prerequisites,
                       c.learning_objectives as LearningObjectives, c.learning_outcomes as LearningOutcomes,
                       c.is_active as IsActive, c.created_date as CreatedDate, c.modified_date as ModifiedDate,
                       c.created_by as CreatedBy, c.modified_by as ModifiedBy
                FROM workflowmgmt.courses c
                INNER JOIN workflowmgmt.semesters s ON c.id = ANY(s.course_id)
                WHERE c.is_active = true AND s.is_active = true";

            var parameters = new DynamicParameters();

            if (degreeId.HasValue)
            {
                sql += " AND s.level_id = @DegreeId";
                parameters.Add("DegreeId", degreeId.Value);
            }

            if (departmentId.HasValue)
            {
                sql += " AND s.department_id = @DepartmentId";
                parameters.Add("DepartmentId", departmentId.Value);
            }

            if (academicYearId.HasValue)
            {
                sql += " AND s.academic_year_id = @AcademicYearId";
                parameters.Add("AcademicYearId", academicYearId.Value);
            }

            if (semesterId.HasValue)
            {
                sql += " AND s.id = @SemesterId";
                parameters.Add("SemesterId", semesterId.Value);
            }

            sql += " ORDER BY c.name";

            return await Connection.QueryAsync<Course>(sql, parameters, transaction: Transaction);
        }

        public async Task<IEnumerable<object>> GetSyllabiByDepartmentAsync(int departmentId)
        {
            const string sql = @"
                SELECT DISTINCT s.id, s.title, 'Syllabus' as type, s.status, s.document_url as url,
                       s.created_date as createdDate, s.modified_date as modifiedDate,
                       s.faculty_name as facultyName, s.is_active as isActive,
                       s.credits, s.duration_weeks as durationWeeks, s.number_of_sessions as numberOfSessions,
                       COUNT(sf.id) as feedbackCount,
                       AVG(CAST(sf.rating as FLOAT)) as averageRating
                FROM workflowmgmt.syllabi s
                INNER JOIN workflowmgmt.courses c ON s.course_id = c.id
                LEFT JOIN workflowmgmt.syllabus_feedback sf ON s.id = sf.syllabus_id AND sf.is_active = 1
                WHERE c.department_id = @DepartmentId AND s.is_active = 1
                GROUP BY s.id, s.title, s.status, s.document_url, s.created_date, s.modified_date,
                         s.faculty_name, s.is_active, s.credits, s.duration_weeks, s.number_of_sessions
                ORDER BY s.created_date DESC";

            return await Connection.QueryAsync<object>(sql, new { DepartmentId = departmentId }, transaction: Transaction);
        }
    }
}
