using WorkFlowViewMgmt.Domain.Entities.Degree;
using WorkFlowViewMgmt.Domain.Entities.Filter;

namespace WorkFlowViewMgmt.Domain.IRepository
{
    public interface IFilterRepository
    {
        // Degree methods (using existing Degree entity)
        Task<IEnumerable<Degree>> GetActiveDegreesAsync();

        // Department methods
        Task<IEnumerable<Department>> GetActiveDepartmentsAsync();
        Task<IEnumerable<Department>> GetDepartmentsByDegreeAsync(int degreeId);

        // Academic Year methods
        Task<IEnumerable<AcademicYear>> GetActiveAcademicYearsAsync();
        Task<IEnumerable<AcademicYear>> GetAcademicYearsByDegreeAsync(int degreeId);
        Task<IEnumerable<AcademicYear>> GetAcademicYearsByDegreeAndDepartmentAsync(int degreeId, int departmentId);

        // Semester methods - filtered by degree, department, and academic year
        Task<IEnumerable<Semester>> GetSemestersByFiltersAsync(int? degreeId, int? departmentId, int? academicYearId);

        // Course methods - filtered by degree, department, academic year, and semester
        Task<IEnumerable<Course>> GetCoursesByFiltersAsync(int? degreeId, int? departmentId, int? academicYearId, int? semesterId);

        // Syllabus methods - get syllabi by department only
        Task<IEnumerable<object>> GetSyllabiByDepartmentAsync(int departmentId);
    }
}
