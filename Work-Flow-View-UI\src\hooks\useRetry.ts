import { useState, useCallback } from 'react';

interface UseRetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  backoffMultiplier?: number;
}

interface UseRetryReturn<T> {
  execute: () => Promise<T>;
  isLoading: boolean;
  error: Error | null;
  retryCount: number;
  canRetry: boolean;
  reset: () => void;
}

export function useRetry<T>(
  asyncFunction: () => Promise<T>,
  options: UseRetryOptions = {}
): UseRetryReturn<T> {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    backoffMultiplier = 2
  } = options;

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const execute = useCallback(async (): Promise<T> => {
    setIsLoading(true);
    setError(null);

    const attemptExecution = async (attempt: number): Promise<T> => {
      try {
        const result = await asyncFunction();
        setRetryCount(0); // Reset on success
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error');
        
        if (attempt < maxRetries) {
          const delay = retryDelay * Math.pow(backoffMultiplier, attempt);
          
          console.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error.message);
          
          await new Promise(resolve => setTimeout(resolve, delay));
          setRetryCount(attempt + 1);
          
          return attemptExecution(attempt + 1);
        } else {
          setError(error);
          throw error;
        }
      }
    };

    try {
      const result = await attemptExecution(0);
      return result;
    } finally {
      setIsLoading(false);
    }
  }, [asyncFunction, maxRetries, retryDelay, backoffMultiplier]);

  const reset = useCallback(() => {
    setError(null);
    setRetryCount(0);
    setIsLoading(false);
  }, []);

  const canRetry = retryCount < maxRetries && !isLoading;

  return {
    execute,
    isLoading,
    error,
    retryCount,
    canRetry,
    reset
  };
}
