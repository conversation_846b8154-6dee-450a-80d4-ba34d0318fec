import React, { useState } from 'react';
import { Download, Trash2, Archive, Share2, CheckSquare, Square, Minus } from 'lucide-react';
import { Document } from '../types';
import { apiConfig } from '../services/config';

interface BulkActionsProps {
  selectedDocuments: Document[];
  onSelectionChange: (documents: Document[]) => void;
  allDocuments: Document[];
  onBulkDownload: (documents: Document[]) => void;
  onBulkAction?: (action: string, documents: Document[]) => void;
}

const BulkActions: React.FC<BulkActionsProps> = ({
  selectedDocuments,
  onSelectionChange,
  allDocuments,
  onBulkDownload,
  onBulkAction
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSelectAll = () => {
    if (selectedDocuments.length === allDocuments.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(allDocuments);
    }
  };

  const getSelectionIcon = () => {
    if (selectedDocuments.length === 0) {
      return <Square className="h-5 w-5" />;
    } else if (selectedDocuments.length === allDocuments.length) {
      return <CheckSquare className="h-5 w-5" />;
    } else {
      return <Minus className="h-5 w-5" />;
    }
  };

  const handleBulkDownload = async () => {
    if (selectedDocuments.length === 0) return;

    setIsProcessing(true);
    try {
      if (selectedDocuments.length === 1) {
        // Single file download
        const doc = selectedDocuments[0];
        const documentUrl = apiConfig.getDocumentUrl(doc.url || '');
        const link = document.createElement('a');
        link.href = documentUrl;
        link.download = doc.title || 'document';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Multiple files - create ZIP
        await createZipDownload(selectedDocuments);
      }
      
      onBulkDownload(selectedDocuments);
    } catch (error) {
      console.error('Bulk download failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const createZipDownload = async (documents: Document[]) => {
    // This would typically use a library like JSZip
    // For now, we'll download files individually with a delay
    for (let i = 0; i < documents.length; i++) {
      const doc = documents[i];
      if (doc.url) {
        const documentUrl = apiConfig.getDocumentUrl(doc.url);
        const link = document.createElement('a');
        link.href = documentUrl;
        link.download = `${doc.type}_${doc.title}`.replace(/[^a-zA-Z0-9]/g, '_');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Small delay between downloads to avoid overwhelming the browser
        if (i < documents.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }
  };

  const handleBulkAction = (action: string) => {
    if (selectedDocuments.length === 0) return;
    onBulkAction?.(action, selectedDocuments);
  };

  if (allDocuments.length === 0) {
    return null;
  }

  return (
    <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Select All Checkbox */}
          <button
            onClick={handleSelectAll}
            className="flex items-center space-x-2 px-3 py-2 rounded-xl hover:bg-gray-100 transition-colors"
            aria-label={
              selectedDocuments.length === allDocuments.length 
                ? 'Deselect all documents' 
                : 'Select all documents'
            }
          >
            {getSelectionIcon()}
            <span className="text-sm font-medium text-gray-700">
              {selectedDocuments.length === 0 && 'Select All'}
              {selectedDocuments.length > 0 && selectedDocuments.length < allDocuments.length && 
                `${selectedDocuments.length} selected`}
              {selectedDocuments.length === allDocuments.length && 'All selected'}
            </span>
          </button>

          {selectedDocuments.length > 0 && (
            <div className="text-sm text-gray-500">
              {selectedDocuments.length} of {allDocuments.length} documents selected
            </div>
          )}
        </div>

        {/* Bulk Actions */}
        {selectedDocuments.length > 0 && (
          <div className="flex items-center space-x-2">
            <button
              onClick={handleBulkDownload}
              disabled={isProcessing}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-xl transition-colors font-medium"
              aria-label={`Download ${selectedDocuments.length} selected documents`}
            >
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">
                {isProcessing ? 'Downloading...' : `Download (${selectedDocuments.length})`}
              </span>
              <span className="sm:hidden">
                {isProcessing ? '...' : selectedDocuments.length}
              </span>
            </button>

            <button
              onClick={() => handleBulkAction('share')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-colors font-medium"
              aria-label={`Share ${selectedDocuments.length} selected documents`}
            >
              <Share2 className="h-4 w-4" />
              <span className="hidden sm:inline">Share</span>
            </button>

            <button
              onClick={() => handleBulkAction('archive')}
              className="flex items-center space-x-2 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-xl transition-colors font-medium"
              aria-label={`Archive ${selectedDocuments.length} selected documents`}
            >
              <Archive className="h-4 w-4" />
              <span className="hidden sm:inline">Archive</span>
            </button>

            <button
              onClick={() => onSelectionChange([])}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-colors font-medium"
              aria-label="Clear selection"
            >
              <span className="text-sm">Clear</span>
            </button>
          </div>
        )}
      </div>

      {/* Selection Summary */}
      {selectedDocuments.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            <div className="text-xs text-gray-600">
              Selected: {selectedDocuments.filter(d => d.type === 'Syllabus').length} Syllabi,{' '}
              {selectedDocuments.filter(d => d.type === 'Lesson').length} Lessons,{' '}
              {selectedDocuments.filter(d => d.type === 'Session').length} Sessions
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BulkActions;
