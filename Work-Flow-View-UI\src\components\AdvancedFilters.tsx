import React, { useState } from 'react';
import { Star, Save, Trash2, Calendar, Filter, BookmarkPlus, Heart, FileText, Clock, Users } from 'lucide-react';
import CustomSelect, { SelectOption } from './CustomSelect';

export interface FilterPreset {
  id: string;
  name: string;
  filters: any;
  createdAt: string;
  isFavorite?: boolean;
}

interface AdvancedFiltersProps {
  onFiltersChange: (filters: any) => void;
  currentFilters: any;
  presets?: FilterPreset[];
  onSavePreset?: (preset: Omit<FilterPreset, 'id' | 'createdAt'>) => void;
  onDeletePreset?: (presetId: string) => void;
  onTogglePresetFavorite?: (presetId: string) => void;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  onFiltersChange,
  currentFilters,
  presets = [],
  onSavePreset,
  onDeletePreset,
  onTogglePresetFavorite
}) => {
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...currentFilters, [key]: value };
    onFiltersChange(newFilters);
    setSelectedPreset(null); // Clear preset selection when filters change
  };

  const handleSavePreset = () => {
    if (!presetName.trim()) return;
    
    onSavePreset?.({
      name: presetName.trim(),
      filters: currentFilters,
      isFavorite: false
    });
    
    setPresetName('');
    setShowSaveDialog(false);
  };

  const handleLoadPreset = (preset: FilterPreset) => {
    onFiltersChange(preset.filters);
    setSelectedPreset(preset.id);
  };

  const hasActiveFilters = () => {
    return Object.values(currentFilters).some(value => {
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(v => v !== '' && v !== null && v !== undefined);
      }
      return value !== '' && value !== null && value !== undefined && value !== 'all';
    });
  };

  return (
    <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Advanced Filters</h3>
        </div>
        
        <div className="flex items-center space-x-2">
          {hasActiveFilters() && (
            <button
              onClick={() => setShowSaveDialog(true)}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
            >
              <Save className="h-4 w-4" />
              <span>Save Preset</span>
            </button>
          )}
          
          <button
            onClick={() => onFiltersChange({})}
            className="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors text-sm"
          >
            Clear All
          </button>
        </div>
      </div>

      {/* Filter Presets */}
      {presets.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Saved Presets</h4>
          <div className="flex flex-wrap gap-2">
            {presets.map((preset) => (
              <div
                key={preset.id}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${
                  selectedPreset === preset.id
                    ? 'bg-blue-100 border-blue-300 text-blue-800'
                    : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <button
                  onClick={() => handleLoadPreset(preset)}
                  className="flex items-center space-x-2"
                >
                  {preset.isFavorite && <Star className="h-3 w-3 text-yellow-500 fill-current" />}
                  <span className="text-sm font-medium">{preset.name}</span>
                </button>
                
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => onTogglePresetFavorite?.(preset.id)}
                    className="p-1 hover:bg-white/50 rounded"
                    title={preset.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                  >
                    <Heart className={`h-3 w-3 ${preset.isFavorite ? 'text-red-500 fill-current' : 'text-gray-400'}`} />
                  </button>
                  
                  <button
                    onClick={() => onDeletePreset?.(preset.id)}
                    className="p-1 hover:bg-red-100 rounded text-red-500"
                    title="Delete preset"
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Advanced Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Date Range Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar className="inline h-4 w-4 mr-1" />
            Date Range
          </label>
          <div className="space-y-2">
            <input
              type="date"
              value={currentFilters.dateRange?.start || ''}
              onChange={(e) => handleFilterChange('dateRange', {
                ...currentFilters.dateRange,
                start: e.target.value
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Start date"
            />
            <input
              type="date"
              value={currentFilters.dateRange?.end || ''}
              onChange={(e) => handleFilterChange('dateRange', {
                ...currentFilters.dateRange,
                end: e.target.value
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="End date"
            />
          </div>
        </div>

        {/* Status Filter */}
        <CustomSelect
          label="Document Status"
          options={[
            { value: 'all', label: 'All Statuses', icon: <FileText className="h-4 w-4 text-gray-600" /> },
            { value: 'Active', label: 'Active', icon: <FileText className="h-4 w-4 text-green-600" /> },
            { value: 'Draft', label: 'Draft', icon: <FileText className="h-4 w-4 text-yellow-600" /> },
            { value: 'Archived', label: 'Archived', icon: <FileText className="h-4 w-4 text-gray-600" /> },
            { value: 'Under Review', label: 'Under Review', icon: <FileText className="h-4 w-4 text-blue-600" /> }
          ]}
          value={currentFilters.status || 'all'}
          onChange={(value) => handleFilterChange('status', value)}
          size="sm"
          variant="outlined"
        />

        {/* Credits Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Credits Range
          </label>
          <div className="flex space-x-2">
            <input
              type="number"
              min="0"
              max="10"
              value={currentFilters.creditsRange?.min || ''}
              onChange={(e) => handleFilterChange('creditsRange', {
                ...currentFilters.creditsRange,
                min: e.target.value
              })}
              placeholder="Min"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <input
              type="number"
              min="0"
              max="10"
              value={currentFilters.creditsRange?.max || ''}
              onChange={(e) => handleFilterChange('creditsRange', {
                ...currentFilters.creditsRange,
                max: e.target.value
              })}
              placeholder="Max"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Duration Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Duration (minutes)
          </label>
          <div className="flex space-x-2">
            <input
              type="number"
              min="0"
              value={currentFilters.durationRange?.min || ''}
              onChange={(e) => handleFilterChange('durationRange', {
                ...currentFilters.durationRange,
                min: e.target.value
              })}
              placeholder="Min"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <input
              type="number"
              min="0"
              value={currentFilters.durationRange?.max || ''}
              onChange={(e) => handleFilterChange('durationRange', {
                ...currentFilters.durationRange,
                max: e.target.value
              })}
              placeholder="Max"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Teaching Method Filter */}
        <CustomSelect
          label="Teaching Method"
          options={[
            { value: 'all', label: 'All Methods', icon: <Users className="h-4 w-4 text-gray-600" /> },
            { value: 'Lecture', label: 'Lecture', icon: <Users className="h-4 w-4 text-blue-600" /> },
            { value: 'Practical', label: 'Practical', icon: <Users className="h-4 w-4 text-green-600" /> },
            { value: 'Workshop', label: 'Workshop', icon: <Users className="h-4 w-4 text-purple-600" /> },
            { value: 'Seminar', label: 'Seminar', icon: <Users className="h-4 w-4 text-orange-600" /> },
            { value: 'Online', label: 'Online', icon: <Users className="h-4 w-4 text-red-600" /> },
            { value: 'Hybrid', label: 'Hybrid', icon: <Users className="h-4 w-4 text-indigo-600" /> }
          ]}
          value={currentFilters.teachingMethod || 'all'}
          onChange={(value) => handleFilterChange('teachingMethod', value)}
          size="sm"
          variant="outlined"
        />

        {/* Favorites Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Favorites
          </label>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={currentFilters.showFavoritesOnly || false}
                onChange={(e) => handleFilterChange('showFavoritesOnly', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Show favorites only</span>
            </label>
          </div>
        </div>
      </div>

      {/* Save Preset Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Save Filter Preset</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preset Name
              </label>
              <input
                type="text"
                value={presetName}
                onChange={(e) => setPresetName(e.target.value)}
                placeholder="Enter preset name..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                autoFocus
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSaveDialog(false)}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSavePreset}
                disabled={!presetName.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors"
              >
                Save Preset
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedFilters;
