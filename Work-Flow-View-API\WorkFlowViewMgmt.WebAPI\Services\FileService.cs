using Microsoft.Extensions.Options;
using WorkFlowViewMgmt.WebAPI.Configuration;
using WorkFlowViewMgmt.WebAPI.Models;

namespace WorkFlowViewMgmt.WebAPI.Services
{
    public class FileService : IFileService
    {
        private readonly FileStorageOptions _fileStorageOptions;
        private readonly ILogger<FileService> _logger;

        public FileService(
            IOptions<FileStorageOptions> fileStorageOptions,
            ILogger<FileService> logger)
        {
            _fileStorageOptions = fileStorageOptions.Value;
            _logger = logger;
        }

        public async Task<FileResult> GetFileAsync(string urlPath)
        {
            try
            {
                // Combine base path with the URL path
                var fullPath = Path.Combine(_fileStorageOptions.BasePath, urlPath.TrimStart('/'));

                if (!File.Exists(fullPath))
                {
                    return new FileResult { Exists = false };
                }

                var fileInfo = new FileInfo(fullPath);
                var content = await File.ReadAllBytesAsync(fullPath);
                var fileName = Path.GetFileName(urlPath);
                var contentType = GetContentType(Path.GetExtension(fileName));

                return new FileResult
                {
                    FileName = fileName,
                    ContentType = contentType,
                    Content = content,
                    Size = fileInfo.Length,
                    LastModified = fileInfo.LastWriteTime,
                    RelativePath = urlPath,
                    FullPath = fullPath,
                    Exists = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving file from path {UrlPath}", urlPath);
                return new FileResult { Exists = false };
            }
        }

        private static string GetContentType(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt" => "text/plain",
                ".png" => "image/png",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".gif" => "image/gif",
                _ => "application/octet-stream"
            };
        }
    }
}
