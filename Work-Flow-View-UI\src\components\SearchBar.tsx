import React, { useState, useRef, useEffect } from 'react';
import { Search, X, Filter, SortAsc, SortDesc, Calendar, FileText, User } from 'lucide-react';
import { useKeyboardNavigation } from '../hooks/useKeyboardNavigation';
import CustomSelect, { SelectOption } from './CustomSelect';

interface SearchBarProps {
  onSearch: (query: string, filters: SearchFilters) => void;
  onClear: () => void;
  placeholder?: string;
  isLoading?: boolean;
}

export interface SearchFilters {
  query: string;
  type?: 'all' | 'Syllabus' | 'Lesson' | 'Session';
  dateRange?: {
    start?: string;
    end?: string;
  };
  sortBy?: 'relevance' | 'date' | 'title' | 'type';
  sortOrder?: 'asc' | 'desc';
  faculty?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  onClear,
  placeholder = "Search documents...",
  isLoading = false
}) => {
  const [query, setQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    type: 'all',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  const inputRef = useRef<HTMLInputElement>(null);
  const filtersRef = useRef<HTMLDivElement>(null);

  useKeyboardNavigation({
    onEscape: () => {
      if (showFilters) {
        setShowFilters(false);
      } else if (query) {
        handleClear();
      }
    },
    onEnter: () => {
      if (query.trim()) {
        handleSearch();
      }
    }
  });

  useEffect(() => {
    // Focus input when component mounts
    inputRef.current?.focus();
  }, []);

  const handleSearch = () => {
    const searchFilters = { ...filters, query: query.trim() };
    onSearch(query.trim(), searchFilters);
  };

  const handleClear = () => {
    setQuery('');
    setFilters({
      query: '',
      type: 'all',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
    onClear();
    inputRef.current?.focus();
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Auto-search when filters change if there's a query
    if (query.trim()) {
      onSearch(query.trim(), { ...newFilters, query: query.trim() });
    }
  };

  const toggleSortOrder = () => {
    const newOrder = filters.sortOrder === 'asc' ? 'desc' : 'asc';
    handleFilterChange('sortOrder', newOrder);
  };

  return (
    <div className="relative w-full max-w-2xl mx-auto">
      {/* Main Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <Search className={`h-5 w-5 ${isLoading ? 'animate-pulse' : ''} text-gray-400`} />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && query.trim()) {
              handleSearch();
            }
          }}
          placeholder={placeholder}
          className="w-full pl-12 pr-20 py-4 bg-white/90 backdrop-blur-sm border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 text-gray-700 font-medium shadow-lg hover:shadow-xl"
          aria-label="Search documents"
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center space-x-2 pr-4">
          {query && (
            <button
              onClick={handleClear}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-xl transition-all duration-200 ${
              showFilters 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
            }`}
            aria-label="Toggle search filters"
            aria-expanded={showFilters}
          >
            <Filter className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <div
          ref={filtersRef}
          className="absolute top-full left-0 right-0 mt-2 bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 z-50 transition-all duration-200 transform opacity-100 scale-100"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Document Type Filter */}
            <CustomSelect
              label="Document Type"
              options={[
                { value: 'all', label: 'All Types', icon: <FileText className="h-4 w-4 text-gray-600" /> },
                { value: 'Syllabus', label: 'Syllabus', icon: <FileText className="h-4 w-4 text-blue-600" /> },
                { value: 'Lesson', label: 'Lesson Plans', icon: <FileText className="h-4 w-4 text-green-600" /> },
                { value: 'Session', label: 'Sessions', icon: <FileText className="h-4 w-4 text-purple-600" /> }
              ]}
              value={filters.type || 'all'}
              onChange={(value) => handleFilterChange('type', value)}
              size="sm"
              variant="outlined"
            />

            {/* Sort By */}
            <CustomSelect
              label="Sort By"
              options={[
                { value: 'relevance', label: 'Relevance', icon: <Search className="h-4 w-4 text-blue-600" /> },
                { value: 'date', label: 'Date', icon: <Calendar className="h-4 w-4 text-green-600" /> },
                { value: 'title', label: 'Title', icon: <FileText className="h-4 w-4 text-purple-600" /> },
                { value: 'type', label: 'Type', icon: <Filter className="h-4 w-4 text-orange-600" /> }
              ]}
              value={filters.sortBy || 'relevance'}
              onChange={(value) => handleFilterChange('sortBy', value)}
              size="sm"
              variant="outlined"
            />

            {/* Sort Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort Order
              </label>
              <button
                onClick={toggleSortOrder}
                className="w-full flex items-center justify-center px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                {filters.sortOrder === 'asc' ? (
                  <>
                    <SortAsc className="h-4 w-4 mr-2" />
                    Ascending
                  </>
                ) : (
                  <>
                    <SortDesc className="h-4 w-4 mr-2" />
                    Descending
                  </>
                )}
              </button>
            </div>

            {/* Faculty Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="inline h-4 w-4 mr-1" />
                Faculty
              </label>
              <input
                type="text"
                value={filters.faculty || ''}
                onChange={(e) => handleFilterChange('faculty', e.target.value)}
                placeholder="Faculty name..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Date Range Filter */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              Date Range
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">From</label>
                <input
                  type="date"
                  value={filters.dateRange?.start || ''}
                  onChange={(e) => handleFilterChange('dateRange', { 
                    ...filters.dateRange, 
                    start: e.target.value 
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">To</label>
                <input
                  type="date"
                  value={filters.dateRange?.end || ''}
                  onChange={(e) => handleFilterChange('dateRange', { 
                    ...filters.dateRange, 
                    end: e.target.value 
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between">
            <button
              onClick={() => {
                setFilters({
                  query: '',
                  type: 'all',
                  sortBy: 'relevance',
                  sortOrder: 'desc'
                });
              }}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              Reset Filters
            </button>
            
            <button
              onClick={() => setShowFilters(false)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
