// Centralized API Configuration
export class ApiConfig {
  private static instance: ApiConfig;
  private _baseUrl: string;

  private constructor() {
    // Use environment variable for API base URL, fallback to production URL
      this._baseUrl = import.meta.env.VITE_API_BASE_URL || 'https://actviewapi.conprgtech.com/api';
    }

  public static getInstance(): ApiConfig {
    if (!ApiConfig.instance) {
      ApiConfig.instance = new ApiConfig();
    }
    return ApiConfig.instance;
  }

  public get baseUrl(): string {
    return this._baseUrl;
  }

  public get feedbackUrl(): string {
    return `${this._baseUrl}/feedback`;
  }

  public get documentsUrl(): string {
    return `${this._baseUrl}/documents`;
  }

  public get academicDataUrl(): string {
    return `${this._baseUrl}/academic-data`;
  }

  public get treeViewUrl(): string {
    return `${this._baseUrl}/tree-view`;
  }

  public get filterUrl(): string {
    return `${this._baseUrl}/filter`;
  }

  // Helper method to get full document URL using the new file API
  public getDocumentUrl(relativePath: string): string {
    if (!relativePath) return '';

    // If it's already a full URL, return as is
    if (relativePath.startsWith('http')) {
      return relativePath;
    }

    // Use the new file API endpoint with the path parameter
    return `${this._baseUrl}/files?path=${encodeURIComponent(relativePath)}`;
  }

  // Helper method to check if we're in development mode
  public get isDevelopment(): boolean {
    return import.meta.env.DEV || import.meta.env.NODE_ENV === 'development';
  }

  // Get development API URL if available
  public get devBaseUrl(): string {
    return import.meta.env.VITE_DEV_API_BASE_URL || 'http://localhost:5000/api';
  }
}

// Export singleton instance
export const apiConfig = ApiConfig.getInstance();
