using System.ComponentModel.DataAnnotations;

namespace WorkFlowViewMgmt.Domain.Entities.Filter
{
    public class Course : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;

        public string? Description { get; set; }

        [Required]
        public int Credits { get; set; }

        [MaxLength(50)]
        public string CourseType { get; set; } = "Core";

        [Required]
        public int DurationWeeks { get; set; }

        [Required]
        public int MaxCapacity { get; set; }

        [MaxLength(20)]
        public string Status { get; set; } = "Active";

        public string? Prerequisites { get; set; }

        public string? LearningObjectives { get; set; }

        public string? LearningOutcomes { get; set; }
    }
}
