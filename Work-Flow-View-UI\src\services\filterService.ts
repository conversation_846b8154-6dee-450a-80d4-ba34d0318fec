import { apiClient } from './api';

// Types for the filter API
export interface Degree {
  id: number;
  name: string;
  active: boolean;
  createdDate?: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Department {
  id: number;
  name: string;
  code: string;
  description?: string;
  headOfDepartment?: string;
  email?: string;
  phone?: string;
  establishedYear?: number;
  programsOffered?: string;
  accreditation?: string;
  status: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  emailNotify?: boolean;
  smsNotify?: boolean;
  inAppNotify?: boolean;
  digestFrequency?: string;
  levelId: number;
}

export interface AcademicYear {
  id: number;
  name: string;
  code: string;
  startYear: number;
  endYear: number;
  levelId: number;
  status: string;
  description?: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Course {
  id: number;
  name: string;
  code: string;
  description?: string;
  credits: number;
  courseType: string;
  durationWeeks: number;
  maxCapacity: number;
  status: string;
  prerequisites?: string;
  learningObjectives?: string;
  learningOutcomes?: string;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}

export interface Semester {
  id: number;
  name: string;
  code: string;
  departmentId: number;
  courseId?: number[];
  startDate: string;
  endDate: string;
  durationWeeks: number;
  totalStudents: number;
  status: string;
  description?: string;
  examScheduled: boolean;
  isActive: boolean;
  createdDate: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  academicYearId: number;
  levelId: number;
}

export interface FilterParams {
  degreeId?: number;
  departmentId?: number;
  academicYearId?: number;
  semesterId?: number;
}

// Filter Service
export class FilterService {
  // Get all active degrees
  async getActiveDegrees(): Promise<Degree[]> {
    const response = await apiClient.get<Degree[]>('/filter/degrees/active');
    return response.data || [];
  }

  // Get all active departments
  async getActiveDepartments(): Promise<Department[]> {
    const response = await apiClient.get<Department[]>('/filter/departments/active');
    return response.data || [];
  }

  // Get departments filtered by degree
  async getDepartmentsByDegree(degreeId: number): Promise<Department[]> {
    const response = await apiClient.get<Department[]>(`/filter/departments/by-degree/${degreeId}`);
    return response.data || [];
  }

  // Get all active academic years
  async getActiveAcademicYears(): Promise<AcademicYear[]> {
    const response = await apiClient.get<AcademicYear[]>('/filter/academic-years/active');
    return response.data || [];
  }

  // Get academic years filtered by degree (level_id)
  async getAcademicYearsByDegree(degreeId: number): Promise<AcademicYear[]> {
    const response = await apiClient.get<AcademicYear[]>(`/filter/academic-years/by-degree/${degreeId}`);
    return response.data || [];
  }

  // Get academic years filtered by degree and department (kept for backward compatibility)
  async getAcademicYearsByDegreeAndDepartment(degreeId: number, departmentId: number): Promise<AcademicYear[]> {
    const response = await apiClient.get<AcademicYear[]>(`/filter/academic-years/by-degree-department?degreeId=${degreeId}&departmentId=${departmentId}`);
    return response.data || [];
  }

  // Get filtered semesters based on degree, department, and academic year
  async getFilteredSemesters(params: FilterParams): Promise<Semester[]> {
    const queryParams = new URLSearchParams();

    if (params.degreeId) queryParams.append('degreeId', params.degreeId.toString());
    if (params.departmentId) queryParams.append('departmentId', params.departmentId.toString());
    if (params.academicYearId) queryParams.append('academicYearId', params.academicYearId.toString());

    const queryString = queryParams.toString();
    const endpoint = queryString ? `/filter/semesters?${queryString}` : '/filter/semesters';

    const response = await apiClient.get<Semester[]>(endpoint);
    return response.data || [];
  }

  // Get filtered courses based on degree, department, academic year, and semester
  async getFilteredCourses(params: FilterParams): Promise<Course[]> {
    const queryParams = new URLSearchParams();

    if (params.degreeId) queryParams.append('degreeId', params.degreeId.toString());
    if (params.departmentId) queryParams.append('departmentId', params.departmentId.toString());
    if (params.academicYearId) queryParams.append('academicYearId', params.academicYearId.toString());
    if (params.semesterId) queryParams.append('semesterId', params.semesterId.toString());

    const queryString = queryParams.toString();
    const endpoint = queryString ? `/filter/courses?${queryString}` : '/filter/courses';

    const response = await apiClient.get<Course[]>(endpoint);
    return response.data || [];
  }

  // Get syllabi by department only
  async getSyllabiByDepartment(departmentId: number): Promise<any[]> {
    const response = await apiClient.get<any[]>(`/filter/syllabi/by-department/${departmentId}`);
    return response.data || [];
  }
}

export const filterService = new FilterService();
