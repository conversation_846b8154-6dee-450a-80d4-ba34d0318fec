import React from 'react';
import { ChevronDown, Filter, Sparkles, Search, RefreshCw, GraduationCap, Building, BookOpen, Calendar, Award } from 'lucide-react';
import { useFilterData } from '../hooks/useFilterData';
import CustomSelect, { SelectOption } from './CustomSelect';

interface FilterPanelProps {
  onFiltersChange?: (filters: any) => void;
}

const FilterPanel: React.FC<FilterPanelProps> = ({ onFiltersChange }) => {
  const { filterData, filters, loading, error, updateFilter, resetFilters } = useFilterData();

  // Notify parent component when filters change
  React.useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange(filters);
    }
  }, [filters, onFiltersChange]);
  const SelectField = ({
    label,
    value,
    onChange,
    options,
    placeholder = "Select...",
    disabled = false,
    icon,
    useNameAsValue = false
  }: {
    label: string;
    value: string | number;
    onChange: (value: string) => void;
    options: any[];
    placeholder?: string;
    disabled?: boolean;
    icon?: React.ReactNode;
    useNameAsValue?: boolean;
  }) => {
    const selectOptions: SelectOption[] = [
      ...options.map((option) => ({
        value: useNameAsValue ? option.name : option.id.toString(),
        label: option.name,
        icon: getIconForOption(label, option)
      }))
    ];

    return (
      <CustomSelect
        label={label}
        options={selectOptions}
        value={value.toString()}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        variant="default"
        size="md"
        className="w-full"
      />
    );
  };

  const getIconForOption = (fieldLabel: string, option: any) => {
    switch (fieldLabel.toLowerCase()) {
      case 'degree':
        return <GraduationCap className="h-4 w-4 text-blue-600" />;
      case 'department':
        return <Building className="h-4 w-4 text-green-600" />;
      case 'course':
        return <BookOpen className="h-4 w-4 text-purple-600" />;
      case 'semester':
        return <Calendar className="h-4 w-4 text-orange-600" />;
      case 'academic year':
        return <Award className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-100/50 p-4 sm:p-6 lg:p-8 mb-8">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/30 rounded-3xl"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl"></div>

      <div className="relative">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-3 sm:space-x-4">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <Filter className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Smart Document Filtering</h2>
              <p className="text-sm sm:text-base text-slate-600 hidden sm:block">Refine your search with cascading academic filters</p>
            </div>
          </div>

          <div className="flex items-center space-x-2 flex-wrap">
            <div className="flex items-center space-x-2 px-3 py-2 sm:px-4 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/50">
              <Sparkles className="h-4 w-4 text-blue-600" />
              <span className="text-xs sm:text-sm font-medium text-blue-700 hidden sm:inline">Cascading Filters</span>
              <span className="text-xs sm:text-sm font-medium text-blue-700 sm:hidden">Filters</span>
            </div>
            <button
              onClick={resetFilters}
              className="flex items-center space-x-2 px-3 py-2 sm:px-4 bg-gradient-to-r from-gray-500/10 to-gray-600/10 rounded-xl border border-gray-200/50 hover:bg-gray-500/20 transition-colors"
              title="Reset all filters"
            >
              <RefreshCw className="h-4 w-4 text-gray-600" />
              <span className="text-xs sm:text-sm font-medium text-gray-700 hidden sm:inline">Reset</span>
            </button>
          </div>
        </div>
        
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6">
          <SelectField
            label="Degree Program"
            value={filters.degree.toString()}
            onChange={(value) => updateFilter('degree', value)}
            options={filterData.degrees}
            placeholder="Degree"
            disabled={loading}
            icon={<div className="w-4 h-4 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full"></div>}
          />

          <SelectField
            label="Department"
            value={filters.department.toString()}
            onChange={(value) => updateFilter('department', value)}
            options={filterData.departments}
            placeholder="Department"
            disabled={loading}
            icon={<div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full"></div>}
          />

          <SelectField
            label="Academic Year"
            value={filters.academicYear.toString()}
            onChange={(value) => updateFilter('academicYear', value)}
            options={filterData.academicYears}
            placeholder="Academic Year"
            disabled={loading}
            useNameAsValue={false}
            icon={<div className="w-4 h-4 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full"></div>}
          />

          <SelectField
            label="Semester"
            value={filters.semester.toString()}
            onChange={(value) => updateFilter('semester', value)}
            options={filterData.semesters}
            placeholder="Semester"
            disabled={loading}
            icon={<div className="w-4 h-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-full"></div>}
          />

          <SelectField
            label="Course"
            value={filters.course.toString()}
            onChange={(value) => updateFilter('course', value)}
            options={filterData.courses}
            placeholder="Course"
            disabled={loading}
            icon={<div className="w-4 h-4 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-full"></div>}
          />
        </div>
        
        {/* Filter Summary */}
        {(filters.degree || filters.department || filters.course || filters.semester || filters.academicYear) && (
          <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200/50">
            <div className="flex items-center space-x-2 mb-3">
              <Search className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-semibold text-blue-700">Active Filters</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {filters.degree && (
                <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">
                  {filterData.degrees.find(d => d.id === Number(filters.degree))?.name}
                </span>
              )}
              {filters.department && (
                <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                  {filterData.departments.find(d => d.id === Number(filters.department))?.name}
                </span>
              )}
              {filters.academicYear && (
                <span className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm font-medium">
                  {filterData.academicYears.find(ay => ay.id === Number(filters.academicYear))?.name}
                </span>
              )}
              {filters.semester && (
                <span className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm font-medium">
                  {filterData.semesters.find(s => s.id === Number(filters.semester))?.name}
                </span>
              )}
              {filters.course && (
                <span className="px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full text-sm font-medium">
                  {filterData.courses.find(c => c.id === Number(filters.course))?.name}
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterPanel;