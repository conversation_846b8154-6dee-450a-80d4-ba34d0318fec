import { useState, useEffect, useCallback } from 'react';
import { UserPreferences } from '../components/UserPreferences';

const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'system',
  layout: 'grid',
  itemsPerPage: 25,
  autoRefresh: false,
  refreshInterval: 5,
  notifications: {
    newDocuments: true,
    updates: true,
    reminders: true
  },
  display: {
    showThumbnails: true,
    showMetadata: true,
    compactMode: false
  },
  downloads: {
    defaultLocation: '',
    autoOpen: false,
    confirmBulkDownload: true
  }
};

const STORAGE_KEY = 'userPreferences';

export function useUserPreferences() {
  const [preferences, setPreferences] = useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [isLoading, setIsLoading] = useState(true);

  // Load preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedPreferences = JSON.parse(stored);
        setPreferences({ ...DEFAULT_PREFERENCES, ...parsedPreferences });
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    if (!isLoading) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
      } catch (error) {
        console.error('Failed to save user preferences:', error);
      }
    }
  }, [preferences, isLoading]);

  // Apply theme to document
  useEffect(() => {
    const applyTheme = () => {
      const root = document.documentElement;
      
      if (preferences.theme === 'system') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        root.classList.toggle('dark', prefersDark);
      } else {
        root.classList.toggle('dark', preferences.theme === 'dark');
      }
    };

    applyTheme();

    // Listen for system theme changes
    if (preferences.theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', applyTheme);
      return () => mediaQuery.removeEventListener('change', applyTheme);
    }
  }, [preferences.theme]);

  const updatePreferences = useCallback((newPreferences: Partial<UserPreferences>) => {
    setPreferences(prev => ({ ...prev, ...newPreferences }));
  }, []);

  const resetPreferences = useCallback(() => {
    setPreferences(DEFAULT_PREFERENCES);
  }, []);

  const exportPreferences = useCallback(() => {
    const dataStr = JSON.stringify(preferences, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'user-preferences.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }, [preferences]);

  const importPreferences = useCallback((file: File) => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const importedPreferences = JSON.parse(e.target?.result as string);
          setPreferences({ ...DEFAULT_PREFERENCES, ...importedPreferences });
          resolve();
        } catch (error) {
          reject(new Error('Invalid preferences file'));
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }, []);

  return {
    preferences,
    isLoading,
    updatePreferences,
    resetPreferences,
    exportPreferences,
    importPreferences
  };
}

// Hook for theme-aware styling
export function useTheme() {
  const { preferences } = useUserPreferences();
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const updateTheme = () => {
      if (preferences.theme === 'system') {
        setIsDark(window.matchMedia('(prefers-color-scheme: dark)').matches);
      } else {
        setIsDark(preferences.theme === 'dark');
      }
    };

    updateTheme();

    if (preferences.theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', updateTheme);
      return () => mediaQuery.removeEventListener('change', updateTheme);
    }
  }, [preferences.theme]);

  return {
    isDark,
    theme: preferences.theme,
    getThemeClass: (lightClass: string, darkClass: string) => 
      isDark ? darkClass : lightClass
  };
}
