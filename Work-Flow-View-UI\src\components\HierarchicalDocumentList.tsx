import React, { useState, useEffect } from 'react';
import { FileText, Download, Eye, Calendar, ChevronRight, ChevronDown, BookOpen, GraduationCap, PlayCircle, Clock, User, Star, MessageSquare } from 'lucide-react';
import { HierarchicalDocument } from '../types';
import DocumentFeedback from './DocumentFeedback';
import LessonFeedbackDisplay from './LessonFeedbackDisplay';
import { feedbackService, FeedbackStats } from '../services/feedbackService';

interface HierarchicalDocumentListProps {
  hierarchicalDocuments: HierarchicalDocument[];
  onViewDocument: (document: any) => void;
  onDownloadDocument: (document: any) => void;
  onToggleExpanded: (itemId: string) => void;
  expandedItems: Set<string>;
  selectedCourse?: any;
  selectedSemester?: any;
  selectedAcademicYear?: any;
  user?: any; // Current logged-in user
}

const HierarchicalDocumentList: React.FC<HierarchicalDocumentListProps> = ({
  hierarchicalDocuments,
  onViewDocument,
  onDownloadDocument,
  onToggleExpanded,
  expandedItems,
  selectedCourse,
  selectedSemester,
  selectedAcademicYear,
  user,
}) => {
  const [feedbackDocument, setFeedbackDocument] = useState<any>(null);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackStats, setFeedbackStats] = useState<Map<string, FeedbackStats>>(new Map());
  const [showFeedbackDisplay, setShowFeedbackDisplay] = useState(false);
  const [selectedFeedbackDocument, setSelectedFeedbackDocument] = useState<any>(null);

  // Load feedback stats for documents
  useEffect(() => {
    const loadFeedbackStats = async () => {
      const statsMap = new Map<string, FeedbackStats>();

      console.log('Loading feedback stats for', hierarchicalDocuments.length, 'documents');

      for (const item of hierarchicalDocuments) {
        if (item.document && (item.type === 'Syllabus' || item.type === 'Lesson')) {
          try {
            console.log('Loading feedback stats for document:', item.document.id, item.document.title);
            const stats = await feedbackService.getLessonFeedbackStats(item.document.id);
            console.log('Received stats for', item.document.id, ':', stats);

            if (stats && stats.totalFeedback > 0) {
              statsMap.set(item.document.id, stats);
            }
          } catch (error) {
            console.error('Error loading feedback stats for', item.document.id, ':', error);
            // Continue loading other stats even if one fails
          }
        }
      }

      console.log('Final feedback stats map:', statsMap);
      setFeedbackStats(statsMap);
    };

    if (hierarchicalDocuments.length > 0) {
      loadFeedbackStats();
    }
  }, [hierarchicalDocuments]);

  const handleFeedback = (document: any) => {
    if (!user) {
      alert('Please login to submit feedback');
      return;
    }
    setFeedbackDocument(document);
    setShowFeedbackModal(true);
  };

  const handleRatingClick = (document: any) => {
    setSelectedFeedbackDocument(document);
    setShowFeedbackDisplay(true);
  };

  const closeFeedbackModal = () => {
    setShowFeedbackModal(false);
    setFeedbackDocument(null);
  };

  const closeFeedbackDisplay = () => {
    setShowFeedbackDisplay(false);
    setSelectedFeedbackDocument(null);
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${star <= rating
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300'
              }`}
          />
        ))}
      </div>
    );
  };

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'Syllabus':
        return <GraduationCap className="h-6 w-6 text-blue-600" />;
      case 'Lesson':
        return <BookOpen className="h-6 w-6 text-emerald-600" />;
      case 'Session':
        return <PlayCircle className="h-6 w-6 text-purple-600" />;
      default:
        return <FileText className="h-6 w-6 text-slate-600" />;
    }
  };

  const getDocumentTypeStyle = (type: string) => {
    switch (type) {
      case 'Syllabus':
        return 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border-blue-300';
      case 'Lesson':
        return 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border-emerald-300';
      case 'Session':
        return 'bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 border-purple-300';
      default:
        return 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 border-slate-300';
    }
  };

  const getCardStyle = (type: string, level: number) => {
    const baseStyle = "group relative transition-all duration-200 hover:shadow-md";

    switch (type) {
      case 'Syllabus':
        return `${baseStyle} bg-blue-50/50 border border-blue-200 hover:border-blue-300`;
      case 'Lesson':
        return `${baseStyle} bg-emerald-50/50 border border-emerald-200 hover:border-emerald-300`;
      case 'Session':
        return `${baseStyle} bg-purple-50/50 border border-purple-200 hover:border-purple-300`;
      default:
        return `${baseStyle} bg-white border border-slate-200 hover:border-slate-300`;
    }
  };

  const renderDocumentItem = (item: HierarchicalDocument, level: number = 0) => {
    const isExpanded = expandedItems.has(item.id);
    const hasChildren = item.children && item.children.length > 0;
    // Improved mobile indentation with better alignment
    const indentClass = level === 0 ? '' : level === 1 ? 'ml-2 sm:ml-6 md:ml-8' : 'ml-4 sm:ml-12 md:ml-16';

    return (
      <div key={item.id} className={`${indentClass}`}>
        <div className={`${getCardStyle(item.type, level)} rounded-lg p-3 sm:p-4 hover:shadow-lg transition-all duration-200`}>
          <div className="flex items-start space-x-3">
            {/* Expand/Collapse Button */}
            {hasChildren && (
              <button
                onClick={() => onToggleExpanded(item.id)}
                className="flex-shrink-0 mt-0.5 p-1 hover:bg-white/80 rounded transition-colors"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-slate-600" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-slate-600" />
                )}
              </button>
            )}

            {/* Document Icon */}
            <div className="flex-shrink-0 p-1.5 bg-white/80 rounded-lg shadow-sm">
              {getDocumentIcon(item.type)}
            </div>

            {/* Document Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <h4
                  className="text-base sm:text-lg font-semibold text-slate-900 cursor-pointer hover:text-blue-600 transition-colors line-clamp-1 flex-1 pr-2"
                  onClick={() => onViewDocument(item.document)}
                >
                  {item.title}
                </h4>
                <span className={`flex-shrink-0 px-2 py-0.5 text-xs font-medium rounded-lg border ${getDocumentTypeStyle(item.type)}`}>
                  {item.type}
                </span>
              </div>

              {hasChildren && (
                <span className="inline-flex items-center text-xs font-medium text-slate-600 bg-slate-100 rounded-full px-2 py-0.5 border border-slate-200 mb-2">
                  {item.children!.length} {item.type === 'Syllabus' ? 'lessons' : 'sessions'}
                </span>
              )}

              <p className="text-sm text-slate-600 mb-3 line-clamp-1">
                {item.document.description}
              </p>
            </div>
          </div>

          {/* Compact Single Line Layout */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 p-2 bg-slate-50/50 rounded-lg border border-slate-200/40">
            {/* Left Side - Compact Info */}
            <div className="flex flex-wrap items-center gap-2 text-xs">
              <div className="flex items-center space-x-1 px-2 py-1 bg-white rounded text-slate-600">
                <Calendar className="h-3 w-3" />
                <span>{item.document.uploadDate ? new Date(item.document.uploadDate).toLocaleDateString() : 'No date'}</span>
              </div>

              <div className="flex items-center space-x-1 px-2 py-1 bg-emerald-50 rounded text-emerald-700">
                <Clock className="h-3 w-3" />
                <span>Updated recently</span>
              </div>

              {/* Compact Rating Display */}
              {(item.type === 'Syllabus' || item.type === 'Lesson') && feedbackStats.has(item.document.id) && (
                <button
                  onClick={() => handleRatingClick(item.document)}
                  className="flex items-center space-x-1 px-2 py-1 bg-yellow-50 hover:bg-yellow-100 rounded transition-colors"
                >
                  {renderStars(Math.round(feedbackStats.get(item.document.id)!.averageRating))}
                  <span className="text-yellow-700 font-medium">
                    {feedbackStats.get(item.document.id)!.averageRating.toFixed(1)}
                  </span>
                </button>
              )}
            </div>

            {/* Right Side - Compact Action Buttons */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => onViewDocument(item.document)}
                className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded text-xs font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
              >
                <Eye className="h-3 w-3 mr-1" />
                View
              </button>

              {/* Compact Feedback Button */}
              {(item.type === 'Syllabus' || item.type === 'Lesson') && (
                <button
                  onClick={() => handleFeedback(item.document)}
                  className="inline-flex items-center px-3 py-1.5 border border-emerald-300 rounded text-xs font-medium text-emerald-700 bg-emerald-50 hover:bg-emerald-100 transition-colors"
                >
                  <MessageSquare className="h-3 w-3 mr-1" />
                  Feedback
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Render Children */}
        {isExpanded && hasChildren && (
          <div className="mt-2 border-l-2 border-blue-200 ml-4 pl-3">
            {item.children!.map(child => renderDocumentItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (hierarchicalDocuments.length === 0) {
    return (
      <div className="w-full bg-white rounded-lg shadow-lg border border-slate-200 p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-400 rounded-lg flex items-center justify-center mx-auto mb-4">
            <FileText className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-slate-900 mb-2">No Documents Available</h3>
          <p className="text-slate-600 text-sm max-w-md mx-auto">
            Please select the required filters above to discover available academic resources.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white rounded-lg shadow-lg border border-slate-200 overflow-hidden">
      {/* Compact Header */}
      <div className="bg-gradient-to-r from-slate-800 to-blue-800 px-4 py-3 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">
            Academic Resources ({hierarchicalDocuments.length})
          </h3>
          {selectedCourse && (
            <div className="flex flex-wrap gap-2 text-xs">
              <span className="flex items-center space-x-1 px-2 py-1 bg-blue-700/30 rounded text-blue-200">
                <BookOpen className="h-3 w-3" />
                <span className="truncate max-w-20">{selectedCourse.name}</span>
              </span>
              {selectedSemester && (
                <span className="flex items-center space-x-1 px-2 py-1 bg-emerald-700/30 rounded text-emerald-200">
                  <Calendar className="h-3 w-3" />
                  <span className="truncate max-w-20">{selectedSemester.name}</span>
                </span>
              )}
              {selectedAcademicYear && (
                <span className="flex items-center space-x-1 px-2 py-1 bg-purple-700/30 rounded text-purple-200">
                  <GraduationCap className="h-3 w-3" />
                  <span className="truncate max-w-20">{selectedAcademicYear.name}</span>
                </span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Compact Document List */}
      <div className="p-4">
        <div className="space-y-3">
          {hierarchicalDocuments.map(item => renderDocumentItem(item))}
        </div>
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && feedbackDocument && (
        <DocumentFeedback
          documentId={feedbackDocument.id}
          documentTitle={feedbackDocument.title}
          documentType={feedbackDocument.type === 'Syllabus' ? 'syllabus' : 'lesson'}
          onClose={closeFeedbackModal}
          user={user}
        />
      )}

      {/* Feedback Display Modal */}
      {showFeedbackDisplay && selectedFeedbackDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Feedback for {selectedFeedbackDocument.title}
              </h2>
              <button
                onClick={closeFeedbackDisplay}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <LessonFeedbackDisplay
                lessonId={selectedFeedbackDocument.id}
                lessonTitle={selectedFeedbackDocument.title}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HierarchicalDocumentList;