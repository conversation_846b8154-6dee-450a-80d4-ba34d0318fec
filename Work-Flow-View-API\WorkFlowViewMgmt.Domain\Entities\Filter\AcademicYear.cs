using System.ComponentModel.DataAnnotations;

namespace WorkFlowViewMgmt.Domain.Entities.Filter
{
    public class AcademicYear : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Code { get; set; } = string.Empty;

        public int StartYear { get; set; }

        public int EndYear { get; set; }

        public int LevelId { get; set; }

        [MaxLength(20)]
        public string Status { get; set; } = "Active";

        public string? Description { get; set; }
    }
}
