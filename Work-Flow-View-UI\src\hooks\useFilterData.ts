import { useState, useEffect, useCallback } from 'react';
import { filterService, Degree, Department, AcademicYear, Course, Semester } from '../services/filterService';

export interface FilterState {
  degree: number | '';
  department: number | '';
  academicYear: number | '';  // This will store the academic year ID, not name
  semester: number | '';
  course: number | '';
}

export interface FilterData {
  degrees: Degree[];
  departments: Department[];
  academicYears: AcademicYear[];
  semesters: Semester[];
  courses: Course[];
}

export interface UseFilterDataReturn {
  filterData: FilterData;
  filters: FilterState;
  loading: boolean;
  error: string | null;
  updateFilter: (key: keyof FilterState, value: string | number) => void;
  resetFilters: () => void;
  loadSyllabiByDepartment: (departmentId: number) => Promise<any[]>;
}

const initialFilterState: FilterState = {
  degree: '',
  department: '',
  academicYear: '',
  semester: '',
  course: ''
};

const initialFilterData: FilterData = {
  degrees: [],
  departments: [],
  academicYears: [],
  semesters: [],
  courses: []
};

export const useFilterData = (): UseFilterDataReturn => {
  const [filterData, setFilterData] = useState<FilterData>(initialFilterData);
  const [filters, setFilters] = useState<FilterState>(initialFilterState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load initial data (only degrees initially)
  const loadInitialData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const degrees = await filterService.getActiveDegrees();

      setFilterData(prev => ({
        ...prev,
        degrees,
        departments: [], // Will be loaded when degree is selected
        academicYears: [], // Will be loaded when degree and department are selected
        semesters: [],
        courses: []
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load degrees');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load departments when degree is selected
  const loadDepartmentsByDegree = useCallback(async (degreeId: number) => {
    if (!degreeId) {
      setFilterData(prev => ({ ...prev, departments: [], academicYears: [], semesters: [], courses: [] }));
      return;
    }

    setLoading(true);
    try {
      const departments = await filterService.getDepartmentsByDegree(degreeId);
      setFilterData(prev => ({
        ...prev,
        departments,
        academicYears: [], // Reset dependent filters
        semesters: [],
        courses: []
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load departments');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load academic years when degree and department are selected
  const loadAcademicYearsByDegreeAndDepartment = useCallback(async (degreeId: number, departmentId: number) => {
    if (!degreeId || !departmentId) {
      setFilterData(prev => ({ ...prev, academicYears: [], semesters: [], courses: [] }));
      return;
    }

    setLoading(true);
    try {
      // Use the new endpoint that filters by degree only, since academic years are linked to degree (level_id)
      const academicYears = await filterService.getAcademicYearsByDegree(degreeId);
      setFilterData(prev => ({
        ...prev,
        academicYears,
        semesters: [], // Reset dependent filters
        courses: []
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load academic years');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load syllabi when department is selected
  const loadSyllabiByDepartment = useCallback(async (departmentId: number) => {
    if (!departmentId) {
      return [];
    }

    setLoading(true);
    try {
      const syllabi = await filterService.getSyllabiByDepartment(departmentId);
      return syllabi;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load syllabi');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Load semesters when academic year is selected
  const loadSemestersByAcademicYear = useCallback(async (degreeId: number, departmentId: number, academicYearId: number) => {
    if (!degreeId || !departmentId || !academicYearId) {
      setFilterData(prev => ({ ...prev, semesters: [], courses: [] }));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const semesters = await filterService.getFilteredSemesters({
        degreeId: degreeId,
        departmentId: departmentId,
        academicYearId: academicYearId
      });

      setFilterData(prev => ({ ...prev, semesters, courses: [] })); // Reset courses when semesters change
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load semesters');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load courses when semester is selected
  const loadCoursesBySemester = useCallback(async (degreeId: number, departmentId: number, academicYearId: number, semesterId: number) => {
    if (!degreeId || !departmentId || !academicYearId || !semesterId) {
      setFilterData(prev => ({ ...prev, courses: [] }));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const courses = await filterService.getFilteredCourses({
        degreeId: degreeId,
        departmentId: departmentId,
        academicYearId: academicYearId,
        semesterId: semesterId
      });

      setFilterData(prev => ({ ...prev, courses }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load courses');
    } finally {
      setLoading(false);
    }
  }, []);

  // Update filter and trigger cascading updates
  const updateFilter = useCallback((key: keyof FilterState, value: string | number) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };

      // Reset dependent filters when parent filter changes
      if (key === 'degree') {
        newFilters.department = '';
        newFilters.academicYear = '';
        newFilters.semester = '';
        newFilters.course = '';

        // Load departments for the selected degree
        if (value) {
          loadDepartmentsByDegree(Number(value));
        } else {
          setFilterData(prev => ({ ...prev, departments: [], academicYears: [], semesters: [], courses: [] }));
        }
      }

      if (key === 'department') {
        newFilters.academicYear = '';
        newFilters.semester = '';
        newFilters.course = '';

        // Load academic years for the selected degree and department
        if (value && newFilters.degree) {
          loadAcademicYearsByDegreeAndDepartment(Number(newFilters.degree), Number(value));
        } else {
          setFilterData(prev => ({ ...prev, academicYears: [], semesters: [], courses: [] }));
        }
      }

      if (key === 'academicYear') {
        newFilters.semester = '';
        newFilters.course = '';

        // Load semesters for the selected degree, department, and academic year
        if (value && newFilters.degree && newFilters.department) {
          loadSemestersByAcademicYear(Number(newFilters.degree), Number(newFilters.department), Number(value));
        } else {
          setFilterData(prev => ({ ...prev, semesters: [], courses: [] }));
        }
      }

      if (key === 'semester') {
        newFilters.course = '';

        // Load courses for the selected semester
        if (value && newFilters.degree && newFilters.department && newFilters.academicYear) {
          loadCoursesBySemester(Number(newFilters.degree), Number(newFilters.department), Number(newFilters.academicYear), Number(value));
        } else {
          setFilterData(prev => ({ ...prev, courses: [] }));
        }
      }

      return newFilters;
    });
  }, [loadDepartmentsByDegree, loadAcademicYearsByDegreeAndDepartment, loadSemestersByAcademicYear, loadCoursesBySemester]);

  // Reset all filters
  const resetFilters = useCallback(() => {
    setFilters(initialFilterState);
    setFilterData(prev => ({
      ...prev,
      semesters: [],
      courses: []
    }));
  }, []);

  // Load initial data on mount
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  return {
    filterData,
    filters,
    loading,
    error,
    updateFilter,
    resetFilters,
    loadSyllabiByDepartment
  };
};
