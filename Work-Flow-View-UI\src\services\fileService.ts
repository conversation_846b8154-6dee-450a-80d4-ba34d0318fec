import { ApiConfig } from './config';

export interface FileInfo {
  name: string;
  extension: string;
  size: number;
  createdDate: string;
  modifiedDate: string;
  relativePath: string;
  url: string;
  folderType: string;
}

export interface FolderInfo {
  name: string;
  path: string;
  fileCount: number;
  totalSize: number;
}

export interface UploadResponse {
  fileName: string;
  originalFileName: string;
  size: number;
  folderType: string;
  url: string;
  uploadedAt: string;
}

export class FileService {
  private apiConfig: ApiConfig;

  constructor() {
    this.apiConfig = ApiConfig.getInstance();
  }

  /**
   * Get file URL using the new API endpoint
   */
  getFileUrl(relativePath: string): string {
    return this.apiConfig.getDocumentUrl(relativePath);
  }
}

// Export singleton instance
export const fileService = new FileService();
